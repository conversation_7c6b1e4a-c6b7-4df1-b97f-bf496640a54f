<?php

namespace StikCredit\Calculators\FreshInstallment;

class DefaultFreshInstallment
{

    public function __construct(
        public int     $index = 0,
        public string  $dueDate = '',
        public bool    $isDue = false,
        public bool    $isPaid = false,
        public bool    $isCurrent = false,
        public bool    $isPartiallyPaid = false,
        public string  $paidDate = '',
        public string  $installmentStatus = '',
        public int     $installmentOverdueDays = 0,
        public int     $installmentPassedDays = 0,

        public int     $totalInstallmentAmount = 0,
        public int     $principalAmount = 0,
        public int     $interestAmount = 0,
        public int     $penaltyAmount = 0,
        public int     $taxesAmount = 0,

        /// accrued amount
        public int     $totalAccruedAmount = 0,
        public int     $accruedPrincipalAmount = 0,
        public int     $accruedInterestAmount = 0,
        public int     $accruedPenaltyAmount = 0,
        public int     $accruedTaxesAmount = 0,

        /// due amount
        public int     $totalDueAmount = 0,
        public int     $duePrincipalAmount = 0,
        public int     $dueInterestAmount = 0,
        public int     $duePenaltyAmount = 0,
        public int     $dueTaxesAmount = 0,

        /// outstanding amount
        public int     $totalOutstandingAmount = 0,
        public int     $totalOutstandingAmountAll = 0,
        public int     $outstandingPrincipalAmount = 0,
        public int     $outstandingInterestAmount = 0,
        public int     $outstandingPenaltyAmount = 0,
        public int     $outstandingTaxesAmount = 0,

        /// overdue amount
        public int     $totalInstallmentOverdueAmount = 0,
        public int     $totalOverdueAmount = 0,
        public int     $overduePrincipleAmount = 0,
        public int     $overdueInterestAmount = 0,
        public int     $overduePenaltyAmount = 0,

        /// paid amount
        public int     $totalPaidAmount = 0,
        public int     $totalPaidAmountAll = 0,
        public int     $paidPrincipalAmount = 0,
        public int     $paidInterestAmount = 0,
        public int     $paidPenaltyAmount = 0,
        public int     $paidTaxesAmount = 0,

        /// profit amount
        public int     $profitAmount = 0,
        public int     $accruedProfitAmount = 0,
        public int     $outstandingProfitAmount = 0,
        public int     $paidProfitAmount = 0,

        /// late amount
        public int     $totalAccruedLateAmount = 0,
        public int     $accruedLatePenaltyAmount = 0,
        public int     $accruedLateInterestAmount = 0,

        public int     $totalOutstandingLateAmount = 0,
        public int     $outstandingLatePenaltyAmount = 0,
        public int     $outstandingLateInterestAmount = 0,

        public int     $totalPaidLateAmount = 0,
        public int     $paidLatePenaltyAmount = 0,
        public int     $paidLateInterestAmount = 0,

        /// others
        public ?string $lastPaymentDate = null,
    )
    {
    }

    public function build(array $args = []): self
    {
        foreach ($args as $propertyName => $propertyVal) {
            if (!property_exists($this, $propertyName)) {
                throw  new \Exception('Invalid property name(' . $propertyName . ')');
            }

            $this->{$propertyName} = $propertyVal;
        }

        $this->buildProfitAmounts();

        return $this;
    }

    public function getLastPaymentDate(): ?string
    {
        if ($this->isPaid) {
            return $this->paidDate;
        }
        return $this->lastPaymentDate;
    }


    protected function buildProfitAmounts(): void
    {
        $this->profitAmount = array_sum([
            $this->penaltyAmount,
            $this->interestAmount,
            $this->totalAccruedLateAmount
        ]);

        $this->accruedProfitAmount = array_sum([
            $this->accruedPenaltyAmount,
            $this->accruedInterestAmount,
            $this->totalAccruedLateAmount
        ]);

        $this->outstandingProfitAmount = array_sum([
            $this->outstandingPenaltyAmount,
            $this->outstandingInterestAmount,
            $this->totalOutstandingLateAmount
        ]);

        $this->paidProfitAmount = array_sum([
            $this->paidPenaltyAmount,
            $this->paidInterestAmount,
            $this->totalPaidLateAmount
        ]);
    }
}
