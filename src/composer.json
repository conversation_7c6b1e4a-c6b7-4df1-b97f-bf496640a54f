{"name": "stikcredit-dev-team/credit-hunter", "type": "project", "version": "1.0.0", "license": "proprietary", "repositories": [{"type": "path", "url": "./custom-repos/calculators"}], "require": {"php": "^8.2", "ext-bcmath": "*", "ext-fileinfo": "*", "ext-intl": "*", "ext-json": "*", "ext-pdo": "*", "ext-redis": "*", "ext-zip": "*", "barryvdh/laravel-dompdf": "^v2.0.1", "barryvdh/laravel-snappy": "^1.0", "barryvdh/laravel-translation-manager": "^0.6.3", "doctrine/dbal": "^3.4", "dyrynda/laravel-model-uuid": "^6.6", "elasticsearch/elasticsearch": "^8.14", "ezyang/htmlpurifier": "^4.16", "fakerphp/faker": "^1.20", "guzzlehttp/guzzle": "^7.5", "ixudra/curl": "^6.21", "kris/laravel-form-builder": "^1.51", "kyslik/column-sortable": "^6.6", "laravel/framework": "^9.0", "laravel/horizon": "^5.0", "laravel/legacy-factories": "^1.3", "laravel/sanctum": "^3.2", "laravel/tinker": "^2.0", "laravel/ui": "^3.0", "league/flysystem-sftp-v3": "^3.0", "livewire/livewire": "^2.12", "maatwebsite/excel": "^3.1", "nwidart/laravel-modules": "^9.0", "pusher/pusher-php-server": "^7.2", "spatie/array-to-xml": "^3.1", "spatie/laravel-data": "^2.0", "spatie/laravel-html": "^3.5", "spatie/laravel-medialibrary": "^10.0.0", "spatie/laravel-permission": "^5.5", "stik-credit/calculators": "dev-master", "symfony/http-client": "^6.1", "symfony/mailgun-mailer": "^6.1"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.7", "barryvdh/laravel-ide-helper": "^2.12", "ekino/phpstan-banned-code": "^1.0", "krlove/eloquent-model-generator": "^2.0", "larastan/larastan": "^2.9", "laravel/telescope": "^4.9", "mockery/mockery": "^1.0", "nunomaduro/collision": "^6.1", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-strict-rules": "^1.4", "phpunit/phpunit": "^9.0", "spatie/laravel-ignition": "^1.4.0"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"php-http/discovery": true}}, "extra": {"laravel": {"dont-discover": ["laravel/telescope"]}}, "autoload": {"psr-4": {"App\\": "app/", "Modules\\": "Mo<PERSON>les/", "StikCredit\\Calculators\\": "vendor/stik-credit/calculators/src/"}, "classmap": ["database"], "files": ["helpers/global_helpers.php"]}, "autoload-dev": {"psr-4": {"App\\": "app/", "Modules\\": "Mo<PERSON>les/", "Tests\\": "tests/"}, "classmap": ["database"]}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}}