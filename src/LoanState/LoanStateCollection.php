<?php

namespace StikCredit\Calculators\LoanState;

use StikCredit\Calculators\Abstract\AbstractLoanCollection;
use StikCredit\Calculators\Accrued\AccruedAmountsCollection;
use StikCredit\Calculators\Config\LoanConfig;
use StikCredit\Calculators\Fees\FeesCollection;
use StikCredit\Calculators\LoanCalculator;
use StikCredit\Calculators\Outstanding\OutstandingAmountCollection;
use StikCredit\Calculators\Overdue\OverdueAmountCollection;
use StikCredit\Calculators\Payments\DefaultPayment;
use StikCredit\Calculators\Payments\ReceivedPaymentsCollection;

class LoanStateCollection extends AbstractLoanCollection
{

    public function build(
        array                      $loanConfig,
        ReceivedPaymentsCollection $receivedPaymentsCollection
    ): LoanStateCollection
    {
        $receivedPaymentsCollection->each(function (DefaultPayment $defaultPayment, int $paymentIndex) use ($loanConfig) {
            $recPayments = [];
            $paymentArr = [];
            foreach ($defaultPayment as $key => $val) {
                $paymentArr[$key] = $val;
            }
            $recPayments[$paymentIndex] = $paymentArr;

            $loanConfig['currentDate'] = $defaultPayment->createdAt;

            $loanConfig['receivedPayments'] = $recPayments;
            LoanCalculator::instance()->build($loanConfig);

            /**
             * @var AccruedAmountsCollection $accruedAmountCollection
             * @var OverdueAmountCollection $overdueAmountCollection
             * @var OutstandingAmountCollection $outstandingAmountCollection
             * @var FeesCollection $feesAmountCollection
             */
            $accruedAmountCollection = $this->diContainer->get(AccruedAmountsCollection::class);
            $overdueAmountCollection = $this->diContainer->get(OverdueAmountCollection::class);
            $outstandingAmountCollection = $this->diContainer->get(OutstandingAmountCollection::class);
            $feesAmountCollection = $this->diContainer->get(FeesCollection::class);

            $defaultLoanState = new DefaultLoanState();

            $defaultLoanState->paymentId = $defaultPayment->paymentId;
            $defaultLoanState->paymentDate = $defaultPayment->createdAt;
            $defaultLoanState->paymentType = $defaultPayment->paymentType;
            $defaultLoanState->accruedPrincipal = $accruedAmountCollection->sum('accruedPrincipleAmount');
            $defaultLoanState->accruedInterest = $accruedAmountCollection->sum('accruedInterestAmount');
            $defaultLoanState->accruedPenalty = $accruedAmountCollection->sum('accruedPenaltyAmount');
            $defaultLoanState->accruedLate = $accruedAmountCollection->getTotalAccruedLateAmount();

            $this->push($defaultLoanState);
        });

        $this->dump();

        return $this;
    }
}
