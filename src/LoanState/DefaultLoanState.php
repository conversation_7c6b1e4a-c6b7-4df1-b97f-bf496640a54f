<?php

namespace StikCredit\Calculators\LoanState;

class DefaultLoanState
{
    public function __construct(
        public int     $paymentId = 0,
        public ?string $paymentDate = null,
        public ?string $paymentType = null,

        public int     $accruedPrincipal = 0,
        public int     $accruedInterest = 0,
        public int     $accruedPenalty = 0,
        public int     $accruedLate = 0,
        public int     $totalAccrued = 0,

        public int     $overdueDays = 0,
        public int     $overduePrincipal = 0,
        public int     $overdueInterest = 0,
        public int     $overduePenalty = 0,
        public int     $overdueLate = 0,
        public int     $totalOverdue = 0,

        public int     $outstandingPrincipal = 0,
        public int     $outstandingInterest = 0,
        public int     $outstandingPenalty = 0,
        public int     $outstandingLate = 0,
        public int     $outstandingOverdue = 0,

        public int     $accruedFees = 0,
        public int     $overdueFees = 0,
        public int     $outstandingFees = 0,
    )
    {
    }
}
