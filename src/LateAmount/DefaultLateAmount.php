<?php

namespace StikCredit\Calculators\LateAmount;

use StikCredit\Calculators\Overdue\OverdueAmountCollection;

class DefaultLateAmount extends AbstractLateAmount
{

    public function __construct(
        public int $index,
        public int $lateInterestAmount = 0,
        public int $latePenaltyAmount = 0,
        public int $outstandingLateInterestAmount = 0,
        public int $outstandingLatePenaltyAmount = 0,
        public int $paidLateInterestAmount = 0,
        public int $paidLatePenaltyAmount = 0,
        public int $overdueDays = 0,
    )
    {
    }

    public function build(): DefaultLateAmount
    {
        $overdueAmountCollection = $this->loanGet(OverdueAmountCollection::class);

        return $this;
    }

    /**
     * @return int
     */
    public function getTotalLateAmount(): int
    {
        $totalLateAmount = array_sum([
            $this->lateInterestAmount,
            $this->latePenaltyAmount,
        ]);

        return intval($totalLateAmount);
    }
}
