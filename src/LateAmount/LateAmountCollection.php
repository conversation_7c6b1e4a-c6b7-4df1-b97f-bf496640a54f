<?php

namespace StikCredit\Calculators\LateAmount;

use StikCredit\Calculators\Abstract\AbstractLoanCollection;

/**
 * @method DefaultLateAmount first()
 * @method DefaultLateAmount last()
 * @method DefaultLateAmount get(int $key, $default = null)
 */
class LateAmountCollection extends AbstractLoanCollection
{

    public function build(): LateAmountCollection
    {
        $numberOfInstallments = $this->getConfig()->getNumberOfInstallments();

        for ($index = 1; $index <= $numberOfInstallments; $index++) {
            $defaultLateAmount = new DefaultLateAmount($index);
            $defaultLateAmount->build();

            $this->put($index, $defaultLateAmount);
        }

        return $this;
    }

    public function getTotalLateAmount(): int
    {
        return 0;
    }
}
