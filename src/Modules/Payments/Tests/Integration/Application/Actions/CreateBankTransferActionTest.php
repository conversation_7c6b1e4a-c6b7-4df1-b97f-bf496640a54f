<?php

namespace Modules\Payments\Tests\Integration\Application\Actions;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\Common\Database\Seeders\Test\ActiveInstallmentsSeeder;
use Modules\Common\Database\Seeders\Test\ApprovedLoanSeeder;
use Modules\Common\Domain\CurrentDate;
use Modules\Common\Enums\Payment\PaymentMethodEnum;
use Modules\Common\Enums\Payment\PaymentPurposeEnum;
use Modules\Common\Enums\Payment\PaymentSourceEnum;
use Modules\Common\Enums\PaymentDirectionEnum;
use Modules\Common\Enums\PaymentProblemEnum;
use Modules\Common\Enums\PaymentStatusEnum;
use Modules\Common\Enums\PaymentTaskNameEnum;
use Modules\Common\Enums\TaskStatusEnum;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\ImportedPayment;
use Modules\Common\Models\Loan;
use Modules\Common\Models\Loan as DbLoan;
use Modules\Common\Models\Payment;
use Modules\Common\Models\PaymentTask;
use Modules\Payments\Application\Actions\CreateBankTransferAction;
use Modules\Payments\Application\Actions\Task\ConfirmTransferToBankAction as Sut;
use Tests\TestCase;

class CreateBankTransferActionTest extends TestCase
{
    use DatabaseTransactions;

    public function testPaymentReceivedFromFile()
    {
        $this->seed(ActiveInstallmentsSeeder::class);
        /** @var Loan $loan */
        $loan = Loan::find(ActiveInstallmentsSeeder::LOAN_ID);
        $sut = app(CreateBankTransferAction::class);
        $ip = new ImportedPayment();
        $ip->created_by = Administrator::DEFAULT_ADMINISTRATOR_ID;
        $ip->credit = 1000;
        $ip->currency_id = 1;
        $ip->payment_basis = PaymentPurposeEnum::LOAN_PAYMENT->value;
        $ip->loan_id = $loan->getKey();
        $ip->client_id = 1;
        $ip->details = 'testing:';
        $ip->more_details = 'bank source';
        $ip->value_date = now()->format('d/m/Y');
        $ip->accounting_date = $ip->value_date;
        $ip->reference_code = 'IBAN1234';
        $ip->document = 'tttt1';
        $ip->client_pin = $loan->client->pin;
        $this->assertEquals('success', $sut->execute($ip));
        $paymentId = $ip->refresh()->payment_id;
        $this->assertNotNull($paymentId);
        $payment = $ip->payment;
        $this->assertEquals(PaymentStatusEnum::DELIVERED, $payment->status);
    }

}
