<?php

namespace Modules\Payments\Tests\Integration\Application\Actions;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\Common\Database\Seeders\Test\ApprovedLoanSeeder;
use Modules\Common\Domain\CurrentDate;
use Modules\Common\Enums\Payment\PaymentMethodEnum;
use Modules\Common\Enums\Payment\PaymentSourceEnum;
use Modules\Common\Enums\PaymentDirectionEnum;
use Modules\Common\Enums\PaymentProblemEnum;
use Modules\Common\Enums\PaymentStatusEnum;
use Modules\Common\Enums\PaymentTaskNameEnum;
use Modules\Common\Enums\TaskStatusEnum;
use Modules\Common\Models\Loan as DbLoan;
use Modules\Common\Models\Payment;
use Modules\Common\Models\PaymentTask;
use Modules\Payments\Application\Actions\ConfirmEasyPayDeliveryAction as Sut;
use Tests\TestCase;

class ConfirmEasyPayDeliveryTest extends TestCase
{
    use DatabaseTransactions;

    public function testEasyPayNotificationChangesStatus()
    {
        $dbLoan = $this->createDbLoan();
        $payment = $this->createPayment($dbLoan);
        $paymentTask = $this->createPaymentTask($payment);
        app()->make(Sut::class)->execute($payment->getKey());
        $payment->refresh();
        $this->assertEquals($payment->status, PaymentStatusEnum::DELIVERED);
        $this->assertNull($payment->paymentTask);
    }

    private function createDbLoan(): DbLoan
    {
        $this->seed(ApprovedLoanSeeder::class);
        $dbLoan = DbLoan::find(ApprovedLoanSeeder::LOAN_ID);
        $dbLoan->payment_method_id = PaymentMethodEnum::EASY_PAY->id();
        $dbLoan->save();
        return $dbLoan;
    }

    private function createPayment(DbLoan $dbLoan): Payment
    {
        $currentDate = new CurrentDate();
        $data = [
            'client_id' => $dbLoan->client_id,
            'loan_id' => $dbLoan->loan_id,
            'office_id' => 1,
            'currency_id' => 1,
            'status' => PaymentStatusEnum::EASY_PAY_SENT,
            'payment_method_id' => $dbLoan->payment_method_id,
            'direction' => PaymentDirectionEnum::OUT,
            'source' => PaymentSourceEnum::EASY_PAY_CURL,
            'amount_received' => 1000,
            'amount_resto' => 0,
            'amount' => 1000,
            'manual' => 1,
            'correction' => 0,
            'created_at' => $currentDate->daysAgo(5),
            'sent_at' => $currentDate->daysAgo(3),
            'created_by' => 2,
        ];
        $payment = (new Payment())->fill($data);
        $payment->save();
        return $payment;
    }

    private function createPaymentTask(Payment $payment): PaymentTask
    {
        $currentDate = new CurrentDate();
        $data = [
            'payment_id' => $payment->getKey(),
            'client_id' => $payment->client_id,
            'loan_id' => $payment->loan_id,
            'payment_method_id' => $payment->payment_method_id,
            'amount' => $payment->amount,
            'status' => TaskStatusEnum::NEW,
            'last_status_update_date' => $currentDate->daysAgo(2),
            'name' => PaymentTaskNameEnum::SEND_EASY_PAY_MANUALLY,
            'direction' => $payment->direction,
            'type' => PaymentProblemEnum::TYPE_LOAN_PAYMENT
        ];
        $paymentTask = (new PaymentTask())->fill($data);
        $paymentTask->save();
        return $paymentTask;
    }

}
