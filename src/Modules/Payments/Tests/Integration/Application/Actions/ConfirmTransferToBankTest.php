<?php

namespace Modules\Payments\Tests\Integration\Application\Actions;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\Common\Database\Seeders\Test\ApprovedLoanSeeder;
use Modules\Common\Domain\CurrentDate;
use Modules\Common\Enums\Payment\PaymentMethodEnum;
use Modules\Common\Enums\Payment\PaymentSourceEnum;
use Modules\Common\Enums\PaymentDirectionEnum;
use Modules\Common\Enums\PaymentProblemEnum;
use Modules\Common\Enums\PaymentStatusEnum;
use Modules\Common\Enums\PaymentTaskNameEnum;
use Modules\Common\Enums\TaskStatusEnum;
use Modules\Common\Models\Loan as DbLoan;
use Modules\Common\Models\Payment;
use Modules\Common\Models\PaymentTask;
use Modules\Payments\Application\Actions\Task\ConfirmTransferToBankAction as Sut;
use Tests\TestCase;

class ConfirmTransferToBankTest extends TestCase
{
    use DatabaseTransactions;

    public function testBankPaymentTaskCreatedOnApprove()
    {
        $dbLoan = $this->createDbLoan();
        $payment = $this->createPayment($dbLoan);
        $paymentTask = $this->createPaymentTask($payment);
        $this->assertEquals($payment->status, PaymentStatusEnum::NEW);
        app()->make(Sut::class)->execute($payment);
        $this->assertEquals($payment->status, PaymentStatusEnum::DELIVERED);
        $paymentTask->refresh();
        $this->assertEquals($paymentTask->status, TaskStatusEnum::DONE);
        $this->assertNull($payment->paymentTask);
    }

    private function createDbLoan(): DbLoan
    {
        $this->seed(ApprovedLoanSeeder::class);
        $dbLoan = DbLoan::find(ApprovedLoanSeeder::LOAN_ID);
        $dbLoan->payment_method_id = PaymentMethodEnum::BANK->id();
        $dbLoan->save();
        return $dbLoan;
    }

    private function createPayment(DbLoan $dbLoan): Payment
    {
        $currentDate = new CurrentDate();
        $data = [
            'client_id' => $dbLoan->client_id,
            'loan_id' => $dbLoan->loan_id,
            'office_id' => 1,
            'currency_id' => 1,
            'status' => PaymentStatusEnum::NEW,
            'payment_method_id' => $dbLoan->payment_method_id,
            'direction' => PaymentDirectionEnum::OUT,
            'source' => PaymentSourceEnum::LOAN_APPROVAL,
            'amount_received' => 1000,
            'amount_resto' => 0,
            'amount' => 1000,
            'manual' => 1,
            'correction' => 0,
            'created_at' => $currentDate->daysAgo(5),
            'sent_at' => $currentDate->daysAgo(3),
            'created_by' => 2,
        ];
        $payment = (new Payment())->fill($data);
        $payment->save();
        return $payment;
    }

    private function createPaymentTask(Payment $payment): PaymentTask
    {
        $currentDate = new CurrentDate();
        $data = [
            'payment_id' => $payment->getKey(),
            'client_id' => $payment->client_id,
            'loan_id' => $payment->loan_id,
            'payment_method_id' => $payment->payment_method_id,
            'amount' => $payment->amount,
            'status' => TaskStatusEnum::NEW,
            'last_status_update_date' => $currentDate->daysAgo(2),
            'name' => PaymentTaskNameEnum::TRANSFER_TO_BANK,
            'direction' => $payment->direction,
            'type' => PaymentProblemEnum::TYPE_LOAN_PAYMENT
        ];
        $paymentTask = (new PaymentTask())->fill($data);
        $paymentTask->save();
        return $paymentTask;
    }

}
