<?php

namespace Modules\Payments\Tests\Acceptance;

use DateTime;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\DB;
use Modules\Api\Application\Actions\NewLoanExistingClientAction;
use Modules\Api\Http\Dto\NewLoanExistingClientDto;
use Modules\Approve\Application\Action\ApproveLoanAction;
use Modules\Approve\Application\Action\ProcessLoanAction;
use Modules\Approve\Presentation\Dto\DecisionDto;
use Modules\Common\Database\Seeders\CashLoanSteps\ActiveCashLoanSeeder;
use Modules\Common\Database\Seeders\Test\AllSteps\AfterLoanApprovalSeeder;
use Modules\Common\Database\Seeders\Test\AllSteps\AfterLoanActivationSeeder;
use Modules\Common\Entities\CommunicationPivot;
use Modules\Common\Enums\Payment\PaymentMethodEnum;
use Modules\Common\Enums\PaymentStatusEnum;
use Modules\Common\Helpers\TestHelpers\TimeMachine;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\ApproveDecision;
use Modules\Common\Models\ApproveDecisionReason;
use Modules\Common\Models\Loan;
use Modules\Common\Models\LoanStatus;
use Modules\Common\Models\Payment;
use Modules\Common\Models\PaymentMethod;
use Modules\Payments\Application\Actions\Task\ConfirmTransferToBankAction;
use Modules\Sales\Application\Actions\NewAppAction;
use Modules\Sales\Application\Actions\SignLoanAction;
use Modules\Sales\Http\Middleware\DtoSerializerNewClientLoan;
use Modules\ThirdParty\Events\EasyPaySendingHasFinished;
use Tests\TestCase;

class ActivateLoanByMakingPaymentTest  extends TestCase
{
    use DatabaseTransactions;

    public function testNormalOutgoingCashPayment()
    {
        $this->seed(AfterLoanApprovalSeeder::class);
        DB::table('cash_operational_transaction')->insert(AfterLoanActivationSeeder::cashOperationalTransaction()[0]);
        DB::statement(
            "SELECT setval('loan_history_loan_history_id_seq', (SELECT MAX(loan_history_id) FROM loan_history)+1);"
        );
        $loanId = AfterLoanApprovalSeeder::LOAN_ID;
        $this->actingAs(Administrator::find(Administrator::DEFAULT_ADMINISTRATOR_ID));
        $response = $this->post(route('head.clientCard.acquireMoney', $loanId));
        $response->assertStatus(302)->assertRedirect(route('head.clients.cardProfile', [1, $loanId]));
        $primaryKeys = [];
        $expectedDatabase = AfterLoanActivationSeeder::database();
        unset(
            $expectedDatabase['loan_contact_actual'],
            $expectedDatabase['client_history'],
            $expectedDatabase['loan_history'],
            $expectedDatabase['loan_status_history'],
            $expectedDatabase['sms'],
            $expectedDatabase['stats_daily'],
            $expectedDatabase['approve_agent_stats'],
            $expectedDatabase['notification_setting']
        );
        foreach ($expectedDatabase as $tableName => $expectedTable) {
            $actualTable = DB::table($tableName)->selectRaw('*')->get()->toArray();
            $this->assertCount(count($expectedTable), $actualTable, $tableName);
            $pk = $tableName . '_id';
            if ($tableName === 'loan_meta') {
                $pk = 'meta_id';
            }
            $primaryKeys[] = $pk;
            if (isset($expectedRow[$pk])) {
                array_multisort(array_column($expectedTable, $pk), SORT_ASC, $expectedTable);
                array_multisort(array_column($actualTable, $pk), SORT_ASC, $actualTable);
            }
            foreach ($expectedTable as $i => $expectedRow) {
                $actualRow = (array) $actualTable[$i];
                foreach ($expectedRow as $key => $expected) {
                    if (in_array($key, $primaryKeys)) {
                        continue;
                    }
                    if (DateTime::createFromFormat('Y-m-d H:i:s', $expected) !== false) {
                        continue;
                    }
                    $idStr = isset($expectedRow[$pk]) ? '(' . $expectedRow[$pk] . ').' : '.';
                    $this->assertEquals(
                        $expected,
                        stripslashes($actualRow[$key]),
                        $tableName . $idStr . $key
                    );
                }
            }
        }
    }

    public function testRefinancingLoanActivationScenario()
    {
        /************ CREATING ACTIVE LOAN ****************/
        $this->seed(ActiveCashLoanSeeder::class);
        $firstLoan = ActiveCashLoanSeeder::$loan->refresh();
        $firstLoanSum = 40000;
        $secondLoanSum = 60000;
        $thirdLoanSum = 80000;
        $this->assertEquals(LoanStatus::ACTIVE_STATUS_ID, $firstLoan->loan_status_id);
        $this->assertEquals($firstLoanSum, $firstLoan->amount_approved);

        /************ PREPARING APPROVED BANK LOAN FROM WEBSITE **********/
        $dto = new NewLoanExistingClientDto(
            '***************',
            'xxx',
            $firstLoan->client_id,
            1,
            $secondLoanSum,
            30,
            PaymentMethodEnum::BANK->id(),
            'BG68FINV91501016892097'
        );
        $data = app(NewLoanExistingClientAction::class)->execute($dto);
        /** @var Loan $secondLoan */
        $secondLoan = Loan::find($data['loan_id']);
        $this->assertNotNull($secondLoan);
        $this->assertCount(1, $secondLoan->refinancing);
        $secondLoan = app(SignLoanAction::class)->execute($secondLoan)->dbModel();
        $secondLoan = app(ProcessLoanAction::class)->execute($secondLoan)->dbModel();

        $secondLoan = app(ApproveLoanAction::class)->execute(new DecisionDto(
            $secondLoan->getKey(),
            1,
            ApproveDecision::APPROVE_DECISION_APPROVED,
            ApproveDecisionReason::APPROVE_DECISION_REASON_OTHER,
            '',
            null
        ));
        $this->assertEquals(LoanStatus::APPROVED_STATUS_ID, $secondLoan->loan_status_id);
        /** @var Payment $secondLoanMainPayment */
        $this->assertCount(2, $secondLoan->payments);
        $secondLoanMainPayment = $secondLoan->payments->first();
        $this->assertEquals(PaymentStatusEnum::NEW, $secondLoanMainPayment->status);
        $this->assertEquals($secondLoanSum - $firstLoanSum, $secondLoanMainPayment->amount);
        $secondLoanOffsetPayment = $secondLoan->payments->last();
        $this->assertEquals(PaymentStatusEnum::NEW, $secondLoanOffsetPayment->status);
        $this->assertEquals($firstLoanSum, $secondLoanOffsetPayment->amount);
        $this->assertEquals($firstLoanSum, $firstLoan->getEarlyRepaymentDebtDb());
        $firstLoanIncomingPayment = $firstLoan->payments->last();
        $this->assertEquals($firstLoanSum, $firstLoanIncomingPayment->amount);
        $this->assertEquals(PaymentStatusEnum::NEW, $firstLoanIncomingPayment->status);


        /************ ACTIVATING LOAN ****************/
        $secondLoanMainPayment = app(ConfirmTransferToBankAction::class)->execute($secondLoanMainPayment);
        $this->assertEquals(PaymentStatusEnum::DELIVERED, $secondLoanMainPayment->status);
        $this->assertEquals(LoanStatus::ACTIVE_STATUS_ID, $secondLoan->refresh()->loan_status_id);
        $this->assertEquals(LoanStatus::REPAID_STATUS_ID, $firstLoan->refresh()->loan_status_id);

        /************ PREPARING APPROVED EASY_PAY LOAN FROM WEBSITE **********/
        $dto = new NewLoanExistingClientDto(
            '***************',
            'xxx',
            $firstLoan->client_id,
            1,
            $thirdLoanSum,
            30,
            PaymentMethodEnum::EASY_PAY->id()
        );
        $data = app(NewLoanExistingClientAction::class)->execute($dto);
        /** @var Loan $secondLoan */
        $thirdLoan = Loan::find($data['loan_id']);
        $this->assertNotNull($thirdLoan);
        $this->assertCount(1, $thirdLoan->refinancing);
        $thirdLoan = app(SignLoanAction::class)->execute($thirdLoan)->dbModel();
        $thirdLoan = app(ProcessLoanAction::class)->execute($thirdLoan)->dbModel();

        $thirdLoan = app(ApproveLoanAction::class)->execute(new DecisionDto(
            $thirdLoan->getKey(),
            1,
            ApproveDecision::APPROVE_DECISION_APPROVED,
            ApproveDecisionReason::APPROVE_DECISION_REASON_OTHER,
            '',
            null
        ));
        $this->assertCount(2, $thirdLoan->payments);
        /** @var Payment $secondLoanMainPayment */
        $thirdLoanMainPayment = $thirdLoan->payments->first();
        EasyPaySendingHasFinished::dispatch($thirdLoanMainPayment, 1);
        $thirdLoan->refresh();
        $this->assertEquals(LoanStatus::ACTIVE_STATUS_ID, $thirdLoan->loan_status_id);
        $this->assertEquals($thirdLoanSum - $secondLoanSum, $thirdLoanMainPayment->amount);
        $this->assertEquals(PaymentStatusEnum::EASY_PAY_SENT, $thirdLoanMainPayment->status);
        $this->assertEquals(LoanStatus::ACTIVE_STATUS_ID, $thirdLoan->refresh()->loan_status_id);
        $this->assertEquals(LoanStatus::REPAID_STATUS_ID, $secondLoan->refresh()->loan_status_id);

    }

    public function testRefinancingViaEasyPayScenario()
    {
        /************ CREATING ACTIVE LOAN ****************/
        $this->seed(ActiveCashLoanSeeder::class);
        $firstLoan = ActiveCashLoanSeeder::$loan->refresh();
        $firstLoanSum = 40000;
        $secondLoanSum = 60000;
        $this->assertEquals(LoanStatus::ACTIVE_STATUS_ID, $firstLoan->loan_status_id);
        $this->assertEquals($firstLoanSum, $firstLoan->amount_approved);

        /*****************Moving first loan back in time ********************/
        $tm = new TimeMachine();
        $client = $tm->sendBackInTime($firstLoan->client, 3);
        $firstLoanDebt = $firstLoan->refresh()->getCredit()->loanCarton()->earlyRepaymentAmount;

        /************ PREPARING APPROVED EASY PAY LOAN FROM WEBSITE **********/
        $dto = new NewLoanExistingClientDto(
            '***************',
            'xxx',
            $firstLoan->client_id,
            1,
            $secondLoanSum,
            30,
            PaymentMethodEnum::EASY_PAY->id()
        );
        $data = app(NewLoanExistingClientAction::class)->execute($dto);
        /** @var Loan $secondLoan */
        $secondLoan = Loan::find($data['loan_id']);
        $this->assertNotNull($secondLoan);
        $this->assertCount(1, $secondLoan->refinancing);
        $secondLoan = app(SignLoanAction::class)->execute($secondLoan)->dbModel();
        $secondLoan = app(ProcessLoanAction::class)->execute($secondLoan)->dbModel();

        $secondLoan = app(ApproveLoanAction::class)->execute(new DecisionDto(
            $secondLoan->getKey(),
            1,
            ApproveDecision::APPROVE_DECISION_APPROVED,
            ApproveDecisionReason::APPROVE_DECISION_REASON_OTHER,
            '',
            null
        ));
        /** @var Payment $secondLoanMainPayment */
        $this->assertCount(2, $secondLoan->payments);
        $secondLoanMainPayment = $secondLoan->payments->first();
        EasyPaySendingHasFinished::dispatch($secondLoanMainPayment, 1);
        $secondLoan->refresh();

        $this->assertEquals(LoanStatus::ACTIVE_STATUS_ID, $secondLoan->loan_status_id);
        $this->assertEquals(LoanStatus::REPAID_STATUS_ID, $firstLoan->refresh()->loan_status_id);
        $this->assertEquals(PaymentStatusEnum::EASY_PAY_SENT, $secondLoanMainPayment->status);
        $this->assertEquals($secondLoanSum - $firstLoanDebt, $secondLoanMainPayment->amount);
        $secondLoanOffsetPayment = $secondLoan->payments->last();
        $this->assertEquals(PaymentStatusEnum::DELIVERED, $secondLoanOffsetPayment->status);
        $this->assertEquals($firstLoanDebt, $secondLoanOffsetPayment->amount);

        $firstLoanIncomingPayment = $firstLoan->payments->last();
        $this->assertEquals($firstLoanDebt, $firstLoanIncomingPayment->amount);
        $this->assertEquals(PaymentStatusEnum::DELIVERED, $firstLoanIncomingPayment->status);
    }
}
