<?php

namespace Modules\Payments\Tests\Acceptance;

use DateTime;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Http\Response;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;
use Modules\Common\Database\Seeders\Test\AllSteps\AfterLoanActivationSeeder;
use Modules\Common\Database\Seeders\Test\AllSteps\AfterLoanRepaymentSeeder;
use Modules\Common\Enums\Payment\PaymentMethodEnum;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\Loan;
use Modules\Head\Services\LoanService;
use Modules\Payments\Exports\PaymentsTestExport;
use Modules\Payments\Imports\PaymentsImport;
use Tests\TestCase;

class RepayLoanByMakingUploadedPaymentTest extends TestCase
{
    use DatabaseTransactions;

    public function testReceivingCashFullManualPayment()
    {
        /// PaymentTaskController:import for auto imported payments
        $this->seed(AfterLoanActivationSeeder::class);
        sleep(1);
        $this->actingAs(Administrator::find(Administrator::DEFAULT_ADMINISTRATOR_ID));

        $filePath = storage_path('test/files/bankUpload.xlsx');
        if(! file_exists($filePath)){
            file_put_contents($filePath, '');
        }
        //Creating file
        Excel::store(new PaymentsTestExport([
            [
                "Референция:",
                "Счет. дата:",
                "Вальор:",
                "Дебит:",
                "Кредит:",
                "Документ:",
                "Получател/Наредител:",
                "Основание за плащане:",
                "Още пояснения:",
                "Още:"
            ],
            [
                "HDOPIUB211090116",
                now()->format('d/m/Y'),
                now()->format('d/m/Y'),
                "",
                "209.68",
                "Получен превод в лева",
                "КАЛОЯН ПАТЛЕЕВ ИЛИЕВ",
                "ЕГН. **********",
                "кредит: ".AfterLoanActivationSeeder::LOAN_ID,
                "BG97BUIN10554246S5A7ET"
            ]
        ]), $filePath);

        //uploading file
        $response = $this->call(
            'POST',
            route('payment.paymentsTasks.import'),
            [],
            $this->prepareCookiesForRequest(),
            ['bankUpload' => new UploadedFile(
                $filePath,
                'bankUpload.xlsx',
                null,
                null,
                true
            )],
            $this->transformHeadersToServerVars([])
        );

        //check response
        $response->assertStatus(302)->assertRedirect(session()->previousUrl());
        /** @var Response $response */
        $this->followRedirects($response)
            ->assertSee(json_encode([
                "total" => 1,
                "duplicates" => 0,
                "paymentsCreated" => 1,
                "paymentTasksCreated" => 0
        ]));


        //check database state
        $expectedDatabase = AfterLoanRepaymentSeeder::database();

        $primaryKeys = [];
        unset(
            //$expectedDatabase['payment'][0]['direction'],//no idea why it keeps changing back and fourth
            $expectedDatabase['payment'][0]['office_id'],
            $expectedDatabase['payment'][0]['payment_method_id'],
            $expectedDatabase['loan_history'],
            $expectedDatabase['client_history'],
            $expectedDatabase['loan_status_history'],
            $expectedDatabase['installment_history'],
            $expectedDatabase['sms'],
            $expectedDatabase['stats_daily'],
            $expectedDatabase['approve_agent_stats'],
        );

        //iterating all tables in dataset
        foreach ($expectedDatabase as $tableName => $expectedTable) {

            $actualTable = DB::table($tableName)->selectRaw('*')->get()->toArray();

            $this->assertCount(count($expectedTable), $actualTable, $tableName);

            //both expected and actual tables must be sorted by Primary Key to make a line by line comparison
            $pk = $tableName . '_id';
            if ($tableName === 'loan_meta') {
                $pk = 'meta_id';
            }

            $primaryKeys[] = $pk;

            if (isset($expectedRow[$pk])) {
                array_multisort(array_column($expectedTable, $pk), SORT_ASC, $expectedTable);
                array_multisort(array_column($actualTable, $pk), SORT_ASC, $actualTable);
            }

            // iterating all records in table
            foreach ($expectedTable as $i => $expectedRow) {

                $actualRow = (array)$actualTable[$i];

                // iterating all columns in record and asserting equality for each one.
                foreach ($expectedRow as $key => $expected) {
                    //primary keys might be different in newly added actual records.
                    if (in_array($key, $primaryKeys)) {
                        continue;
                    }
                    $idStr = isset($expectedRow[$pk]) ? '(' . $expectedRow[$pk] . ').' : '.';
                    //datetime may be off by 1 second, so I am being lazy and just checking existence
                    if (is_string($expected) && (DateTime::createFromFormat('Y-m-d H:i:s', $expected) !== false
                            || DateTime::createFromFormat('Y-m-d', $expected) !== false)) {
                        $this->assertNotNull($actualRow[$key], $tableName . $idStr . $key);
                        continue;
                    }

                    $this->assertEquals(
                        $expected,
                        stripslashes($actualRow[$key]),
                        $tableName . $idStr . $key
                    );
                }
            }
        }
    }
}
