<?php

namespace Modules\Payments\Tests\Acceptance;

use DateTime;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\DB;
use Modules\Common\Database\Seeders\Test\AllSteps\AfterLoanActivationSeeder;
use Modules\Common\Database\Seeders\Test\AllSteps\AfterLoanRepaymentSeeder;
use Modules\Common\Enums\Payment\PaymentMethodEnum;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\BankAccount;
use Tests\TestCase;

class RepayLoanByMakingManualPaymentTest extends TestCase
{
    use DatabaseTransactions;

    public function testReceivingCashFullManualPayment()
    {
        /// for all manual payment used: ManualPaymentController
        $this->seed([
            AfterLoanActivationSeeder::class,
            //BankAccountSeeder::class
        ]);
        sleep(1);
        $this->actingAs(Administrator::find(Administrator::DEFAULT_ADMINISTRATOR_ID));
        $postData = [
            "clientIds" => 1,
            "selectedLoanIds" => [AfterLoanActivationSeeder::LOAN_ID => AfterLoanActivationSeeder::LOAN_ID],
            "totalAmountForDelivery" => 209.68,
            "loanPaymentAmount" => [
                AfterLoanActivationSeeder::LOAN_ID => 20968
            ],
            "loanAction" => [AfterLoanActivationSeeder::LOAN_ID => 'loan_payment'],
            "office_id" => 33,
            "payment_method_id" => PaymentMethodEnum::CASH->id(),
            "bank_account_id" => BankAccount::all()->first()?->getKey(),
            "payment_amount" => 209.68,
            "receive_cash" => 209.68,
            "document_number" => '200000',
            "description" => '200000',
            "loans" => [AfterLoanActivationSeeder::LOAN_ID],
        ];

        $response = $this->post(route('payment.manual-payment.storeManualPayment'), $postData);

        $response->assertStatus(200)->assertJson([
            'status' => true,
            'reload' => true,
        ]);

        $expectedDatabase = AfterLoanRepaymentSeeder::database();
        $expectedDatabase['payment'][1]['payment_method_id'] = 3;
        $expectedDatabase['payment'][1]['source'] = 'manual_creation';
        $expectedDatabase['payment'][1]['office_id'] = 33;
        $expectedDatabase['payment'][1]['description'] = '200000';
        $expectedDatabase['payment'][1]['purpose'] = 'loan_payment';
        $expectedDatabase['cash_operational_transaction'][] = json_decode('{
    "cash_operational_transaction_id": 3,
    "office_id": 33,
    "transaction_type": "loan_payment",
    "direction": "in",
    "amount": "20968",
    "amount_signed": "20968",
    "created_at": "' . now() . '"
  }', true);
        $primaryKeys = [];
        unset(
            //$expectedDatabase['payment'][0]['direction'],//jumps from in to out on make test command
            $expectedDatabase['sms'],
            $expectedDatabase['stats_daily'],
            $expectedDatabase['approve_agent_stats'],
            $expectedDatabase['installment_history'],
            $expectedDatabase['loan_history'],
            $expectedDatabase['loan_status_history'],
        );
        //iterating all tables in dataset
        foreach ($expectedDatabase as $tableName => $expectedTable) {

            $actualTable = DB::table($tableName)->selectRaw('*')->get()->toArray();

            $this->assertCount(count($expectedTable), $actualTable, $tableName);

            //both expected and actual tables must be sorted by Primary Key to make a line by line comparison
            $pk = $tableName . '_id';
            if ($tableName === 'loan_meta') {
                $pk = 'meta_id';
            }

            $primaryKeys[] = $pk;

            if (isset($expectedRow[$pk])) {
                array_multisort(array_column($expectedTable, $pk), SORT_ASC, $expectedTable);
                array_multisort(array_column($actualTable, $pk), SORT_ASC, $actualTable);
            }

            // iterating all records in table
            foreach ($expectedTable as $i => $expectedRow) {

                $actualRow = (array)$actualTable[$i];

                // iterating all columns in record and asserting equality for each one.
                foreach ($expectedRow as $key => $expected) {
                    //primary keys might be different in newly added actual records.
                    if (in_array($key, $primaryKeys)) {
                        continue;
                    }
                    //datetime may be off by 1 second, so I am being lazy and just checking existence
                    if (is_string($expected) &&
                        (DateTime::createFromFormat('Y-m-d H:i:s', $expected) !== false
                            || DateTime::createFromFormat('Y-m-d', $expected) !== false)
                    ) {
                        $this->assertNotNull($actualRow[$key], $tableName .'.'. $key);
                        continue;
                    }
                    $idStr = isset($expectedRow[$pk]) ? '(' . $expectedRow[$pk] . ').' : '.';
                    $this->assertEquals(
                        $expected,
                        stripslashes($actualRow[$key]),
                        $tableName . $idStr . $key
                    );
                }
            }
        }
    }
}
