<?php

namespace Modules\Common\FilterForms;

use <PERSON>\LaravelFormBuilder\Form;
use <PERSON>\LaravelFormBuilder\FormBuilder;
use Modules\Admin\Repositories\AdministratorRepository;
use Modules\Head\Repositories\LoanRepository;
use Modules\Head\Services\LoanService;

class BaseForm extends Form
{
    public function getFormName(): string
    {
        $className = explode('\\', get_class($this));

        return last($className);
    }

    public function addOfficeIdSelect(bool $required = false): self
    {
        $offices = app(AdministratorRepository::class)->getAdminOffices()
            ->pluck('name', 'office_id')
            ->toArray();

        $this->add('office_id', 'select', [
            'label' => __('table.Office'),
            'empty_value' => __('table.SelectOption'),
            'choices' => $offices,
            'attr' => [
                'data-live-search' => 'true',
                'required' => $required
            ]
        ]);

        return $this;
    }

    public static function create(array $args = []): Form
    {
        return app(FormBuilder::class)->create(static::class, $args);
    }
}
