<?php

namespace Modules\Sales\Tests\Integration\Application\Actions;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\Approve\Application\Action\CancelLoanAction as Sut;
use Modules\Approve\Presentation\Dto\DecisionDto;
use Modules\Common\Database\Seeders\Test\NewPayday30LoanSeeder;
use Modules\Common\Database\Seeders\Test\ProcessedLoanSeeder;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\ApproveDecision;
use Modules\Common\Models\ApproveDecisionReason;
use Modules\Common\Models\Loan;
use Modules\Common\Models\Office;
use Modules\Communication\Models\EmailTemplate;
use Modules\Communication\Repositories\EmailTemplateRepository;
use Tests\TestCase;

class CancelLoanActionTest extends TestCase
{
    use DatabaseTransactions;

    public function setUp(): void
    {
        parent::setUp();
    }

    public function testBuilding()
    {
        //$t = app(EmailTemplateRepository::class)->getTemplateByKeyAndOffice(EmailTemplate::LOAN_REJECTED,  Office::OFFICE_ID_WEB);
        //dd($t);
        //dd(EmailTemplate::where(['key' => EmailTemplate::LOAN_REJECTED])->first());
        $this->seed(ProcessedLoanSeeder::class);
        $sut = app()->make(Sut::class);
        $res = $sut->execute(DecisionDto::from([
            'admin_id'=>Administrator::DEFAULT_ADMINISTRATOR_ID,
            'loan_id'=>ProcessedLoanSeeder::LOAN_ID,
            'decision'=>ApproveDecision::APPROVE_DECISION_CANCELED,
            'decision_reason'=>ApproveDecisionReason::APPROVE_DECISION_REASON_DISAPPROVE_AMOUNT
        ]));
        $this->assertEquals(__('head::loanCrud.loanSuccessfullyCancelled'), $res['cancelLoanMsg']);
        $this->assertTrue($res['canceled']);
    }

}
