<?php

namespace Modules\Sales\Tests\Integration\Application\Actions;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\App;
use Modules\Common\Models\Client as DbClient;
use Modules\Common\Providers\TestDataProvider;
use Modules\Head\Repositories\ClientRepository;
use Modules\Sales\Application\Actions\NewClientNoLoanAction;
use Modules\Sales\Domain\Entities\Client\Client;
use Modules\Sales\Http\Dto\ClientDto;
use Modules\Sales\Http\Middleware\DtoSerializerNewClientLoan;
use Tests\TestCase;

class NewClientNoLoanActionTest extends TestCase
{
    use DatabaseTransactions;

    private ClientDto $clientDto;
    private ClientRepository $clientRepo;
    private array $existingAddressData;

    public function setUp(): void
    {
        parent::setUp();
        $this->clientRepo = new ClientRepository(new DbClient());
        $this->sut = new NewClientNoLoanAction(app(Client::class));
    }

    public function testCreatingNewClientHappyPath()
    {
        $dto = TestDataProvider::get(
            'applicationRequest1',
            DtoSerializerNewClientLoan::class,
            'createClientDto'
        );
        $this->sut->execute($dto);
        $client = $this->sut->getClient();
        $this->assertEquals($dto->first_name, $client->dbModel()->first_name);
        //$this->assertEquals($dto->loanDto->loanSum, $this->sut->getDbLoan()->amount_approved);
        $this->assertEquals($dto->relationDto->idCardDto->pin, $client->relations()->idCard()->dbModel()->pin);
        /*TODO: continue asserting that each piece of provided data is retrievable from client */
    }
}
