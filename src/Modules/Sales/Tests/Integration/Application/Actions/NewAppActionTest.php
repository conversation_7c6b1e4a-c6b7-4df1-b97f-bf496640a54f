<?php

namespace Modules\Sales\Tests\Integration\Application\Actions;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\Common\Models\Client as DbClient;
use Modules\Common\Models\ClientIdCard;
use Modules\Common\Models\Product;
use Modules\Common\Models\ProductSetting;
use Modules\Common\Models\Sms;
use Modules\Common\Providers\TestDataProvider;
use Modules\Head\Repositories\ClientRepository;
use Modules\Sales\Application\Actions\NewAppAction;
use Modules\Sales\Domain\Entities\Client\IdCard;
use Modules\Sales\Http\Dto\ClientDto;
use Modules\Sales\Http\Middleware\DtoSerializerNewClientLoan;
use Tests\TestCase;

class NewAppActionTest extends TestCase
{
    use DatabaseTransactions;

    private ClientDto $clientDto;
    private ClientRepository $clientRepo;
    private array $existingAddressData;

    public function setUp(): void
    {
        parent::setUp();
        $this->clientRepo = new ClientRepository(new DbClient());
        $this->sut = app()->make(NewAppAction::class);
    }

    public function testCreatingNewClientHappyPath()
    {
        /** @var ClientDto $dto */
        $dto = TestDataProvider::get(
            'applicationRequest1',
            DtoSerializerNewClientLoan::class,
            'createClientDto'
        );
        $setting = Product::find(1)->productSettings()->where('name', ProductSetting::SEND_SMS_KEY)->firstOrFail();
        $setting->value = 1;
        $setting->save();
        $this->sut->execute($dto);
        $client = $this->sut->getClient();
        $this->assertEquals($dto->first_name, $client->dbModel()->first_name);
        //$this->assertEquals($dto->loanDto->loanSum, $this->sut->getDbLoan()->amount_approved);
        $this->assertEquals($dto->relationDto->idCardDto->pin, $client->relations()->idCard()->dbModel()->pin);
        /*TODO: continue asserting that each piece of provided data is retrievable from client */
        //TEST SMS SENT
        //TODO: figure out why this is not working, and create additional test for repaid loan
        $smses = Sms::all();
        $this->assertCount(1, $smses);
        //$this->assertEquals('OK',$smses[0]->response);
    }

    public function testCreatingLegalEntityHappyPath()
    {
        /** @var ClientDto $dto */
        $dto = TestDataProvider::get(
            'legalEntityRequest',
            DtoSerializerNewClientLoan::class,
            'createClientDto'
        );
        $this->sut->execute($dto);
        $client = $this->sut->getClient();
        $this->assertEquals($dto->first_name, $client->dbModel()->first_name);
        $this->assertEquals($dto->relationDto->idCardDto->pin, $client->relations()->idCard()->dbModel()->pin);
        $representatives = $client->dbModel()->clientRepresentors;
        $this->assertCount(1, $representatives);
        $this->assertEquals('Representative', $representatives[0]->representor->first_name);
    }

    public function testNewAppForOldClientWithExpiredCard()
    {
        /** @var ClientDto $dto */
        $dto = TestDataProvider::get(
            'applicationRequest1',
            DtoSerializerNewClientLoan::class,
            'createClientDto'
        );
        $dto->relationDto->representativeDto = null;
        $client = app(NewAppAction::class)->execute($dto);
        $dbClient = $client->dbModel();
        $dbClient->loans->last()->delete();
        /** @var ClientIdCard $clientIdCard */
        $clientIdCard = $dbClient->clientIdCards->last();
        $clientIdCard->valid_date = now()->subDays(10);
        $clientIdCard->save();

        $dto->idcard_number = **********;
        $dto->relationDto->idCardDto->idcard_number = **********;
        app(NewAppAction::class)->execute($dto);
        $this->assertEquals('2024-03-09', $dbClient->refresh()->clientIdCards->first()->valid_date);
        $this->assertCount(1, $dbClient->loans);
    }
}
