<?php

namespace Modules\Sales\Tests\Integration\Application\Console;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Carbon;
use Modules\Approve\Application\Action\ApproveLoanAction;
use Modules\Approve\Application\Action\ProcessLoanAction;
use Modules\Approve\Presentation\Dto\DecisionDto;
use Modules\Common\Enums\PaymentDeliveryEnum;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\ApproveDecision;
use Modules\Common\Models\ApproveDecisionReason;
use Modules\Common\Models\Office;
use Modules\Common\Models\Payment;
use Modules\Common\Models\PaymentMethod;
use Modules\Common\Providers\TestDataProvider;
use Modules\Head\Console\CcrReportOut;
use Modules\Common\Models\CcrReportOut as DbModel;
use Modules\Payments\Application\Actions\ManualPaymentSaveAction;
use Modules\Payments\Application\Actions\Task\ConfirmEasyPaySendingAction;
use Modules\Sales\Application\Actions\ExistingClientCreateLoanAction;
use Modules\Sales\Application\Actions\NewAppAction;
use Modules\Sales\Application\Actions\NewClientNoLoanAction;
use Modules\Sales\Application\Actions\SaveEditedClientDataAction;
use Modules\Sales\Application\Actions\SignLoanAction;
use Modules\Sales\Http\Dto\AddressDto;
use Modules\Sales\Http\Dto\ClientDto;
use Modules\Sales\Http\Middleware\DtoSerializerNewClientLoan;
use Tests\TestCase;

class CcrReportOutTest extends TestCase
{
    use DatabaseTransactions;

    public function setUp(): void
    {
        parent::setUp();
    }

    public function testHappyPath()
    {
        /** @var ClientDto $data */
        $data = TestDataProvider::get(
            'applicationRequest1',
            DtoSerializerNewClientLoan::class,
            'createClientDto'
        );
        $domClient = app(NewAppAction::class)->execute($data);
        $newClient = $domClient->dbModel();
        $this->assertEquals(0, $newClient->registered_in_ccr);
        $this->assertEquals(1, $newClient->need_ccr_sync);
        $this->assertNull($newClient->registered_in_ccr_at);
        $this->assertEquals(now()->format('Y-m-d'), Carbon::parse($newClient->set_need_ccr_sync_at)->format('Y-m-d'));
        $this->assertNull($newClient->unset_need_ccr_sync_at);

        $newLoan = $domClient->dbLoan();
        $this->assertEquals(0, $newLoan->registered_in_ccr);
        $this->assertEquals(0, $newLoan->need_ccr_sync);
        $this->assertNull($newLoan->registered_in_ccr_at);
        $this->assertNull($newLoan->set_need_ccr_sync_at);
        $this->assertNull($newLoan->unset_need_ccr_sync_at);
        $this->assertEquals(0, $newLoan->ccr_finished);
        $this->assertNull($newLoan->ccr_finished_at);

        //running script after creation
        $this->artisan(CcrReportOut::name(), ['type'=>DbModel::TYPE_CLIENT])->assertSuccessful();
        //client
        $this->assertEquals(0, $newClient->refresh()->need_ccr_sync);
        $this->assertEquals(1, $newClient->registered_in_ccr);
        //loan
        $this->assertEquals(0, $newLoan->refresh()->need_ccr_sync);//initial value
        $this->assertEquals(null, $newLoan->registered_in_ccr_at);//initial value


        /******************************ACTIVATE LOAN **************************/
        $signedLoan = app(SignLoanAction::class)->execute($newLoan)->dbModel();
        $processedLoan = app(ProcessLoanAction::class)->execute($signedLoan)->dbModel();
        $approvedLoan = app(ApproveLoanAction::class)->execute(new DecisionDto(
            $processedLoan->getKey(),
            1,
            ApproveDecision::APPROVE_DECISION_APPROVED,
            ApproveDecisionReason::APPROVE_DECISION_REASON_OTHER,
            '',
            null
        ));
        $payout = Payment::all()->first();

        $activeLoan = app(ConfirmEasyPaySendingAction::class)->execute($payout)->loan;
        $this->assertTrue($activeLoan->isActive());


        $this->assertEquals(1, $activeLoan->need_ccr_sync);
        $this->assertEquals(now()->format('Y-m-d'), Carbon::parse($activeLoan->set_need_ccr_sync_at)->format('Y-m-d'));

        //running script after activation
        $this->artisan(CcrReportOut::name(), ['type'=>DbModel::TYPE_LOAN])->assertSuccessful();

        $activeLoan = $activeLoan->refresh();
        $this->assertEquals(0, $activeLoan->need_ccr_sync);
        $this->assertEquals(1, $activeLoan->registered_in_ccr);
        //$this->assertNull($activeLoan->set_need_ccr_sync_at);
        $this->assertEquals(now()->format('Y-m-d'), Carbon::parse($activeLoan->registered_in_ccr_at)->format('Y-m-d'));


        /**********************EXTEND LOAN *****************************/
        $data = [
            'loans'=>[$activeLoan->getKey()],
            'loanPaymentAmount'=>[$activeLoan->getKey()=>$activeLoan->amount_approved],
            'loanAction'=>[$activeLoan->getKey()=>PaymentDeliveryEnum::DELIVERY_LOAN_EXTENSION->value],
            'payment_method_id'=>PaymentMethod::PAYMENT_METHOD_BANK,
            'payment_amount'=>$activeLoan->amount_approved,
            'office_id'=>Office::OFFICE_ID_WEB,
            'description'=>'test',
            'document_number'=>'test123',
            'payment_task_id'=>null,
            'payment_task_decision_id'=>null,
            'admin_id'=>2
        ];
        app(ManualPaymentSaveAction::class)->execute($data);
        $extendedLoan = $activeLoan->refresh();
        $this->assertEquals(1, $extendedLoan->need_ccr_sync);
        $this->assertEquals(now()->format('Y-m-d'), Carbon::parse($extendedLoan->set_need_ccr_sync_at)->format('Y-m-d'));

        //running script after EXTENSION
        $this->artisan(CcrReportOut::name(), ['type'=>'cred'])->assertSuccessful();

        $newClient = $newClient->refresh();
        $extendedLoan = $extendedLoan->refresh();
        $this->assertEquals(0, $extendedLoan->need_ccr_sync);
        $this->assertEquals(now()->format('Y-m-d'), Carbon::parse($extendedLoan->unset_need_ccr_sync_at)->format('Y-m-d'));
        $this->assertEquals(1, $extendedLoan->registered_in_ccr);
        $this->assertEquals(now()->format('Y-m-d'), Carbon::parse($extendedLoan->registered_in_ccr_at)->format('Y-m-d'));

        /******************************REPAY LOAN ***************************/
        $amount = $extendedLoan->getRegularRepaymentDebtDb();
        $data = [
            'loans'=>[$extendedLoan->getKey()],
            'loanPaymentAmount'=>[$extendedLoan->getKey()=>$amount],
            'loanAction'=>[$extendedLoan->getKey()=>PaymentDeliveryEnum::DELIVERY_LOAN_PAYMENT->value],
            'payment_method_id'=>PaymentMethod::PAYMENT_METHOD_BANK,
            'payment_amount'=>$amount,
            'office_id'=>Office::OFFICE_ID_WEB,
            'description'=>'test',
            'document_number'=>'test123',
            'payment_task_id'=>null,
            'payment_task_decision_id'=>null,
            'admin_id'=>Administrator::DEFAULT_ADMINISTRATOR_ID
        ];
        app(ManualPaymentSaveAction::class)->execute($data);
        $repaidLoan = $extendedLoan->refresh();
        $this->assertTrue($repaidLoan->isRepaid());
        $this->assertEquals(1, $repaidLoan->need_ccr_sync);

        //running script after REPAYMENT
        $this->artisan(CcrReportOut::name(), ['type'=> DbModel::TYPE_MONTHLY])->assertSuccessful();
        
        $newClient = $newClient->refresh();
        $repaidLoan = $repaidLoan->refresh();
        $this->assertEquals(0, $newClient->need_ccr_sync);
        $this->assertEquals(1, $repaidLoan->ccr_finished);
        $this->assertEquals(now()->format('Y-m-d'), Carbon::parse($repaidLoan->ccr_finished_at)->format('Y-m-d'));
        $this->assertEquals(0, $repaidLoan->need_ccr_sync);
    }

    public function testSeparateCreation()
    {
        /** @var ClientDto $dto */
        $dto = TestDataProvider::get(
            'applicationRequest1',
            DtoSerializerNewClientLoan::class,
            'createClientDto'
        );
        $loanDto = $dto->relationDto->loanDto;
        $dto->relationDto->loanDto = null;

        //Creating client
        $newClient = app(NewClientNoLoanAction::class)->execute($dto)->dbModel();
        $newClient->legal_status = 'individual';
        $newClient->save();
        $this->assertEquals(0, $newClient->registered_in_ccr);
        $this->assertEquals(1, $newClient->need_ccr_sync);
        $this->assertNull($newClient->registered_in_ccr_at);
        $this->assertEquals(now()->format('Y-m-d'), Carbon::parse($newClient->set_need_ccr_sync_at)->format('Y-m-d'));
        $this->assertNull($newClient->unset_need_ccr_sync_at);

        //creating loan
        $newLoan = app(ExistingClientCreateLoanAction::class)->execute($newClient, $loanDto)->dbLoan();
        $this->assertEquals(0, $newLoan->registered_in_ccr);
        $this->assertEquals(0, $newLoan->need_ccr_sync);
        $this->assertNull($newLoan->registered_in_ccr_at);
        $this->assertNull($newLoan->set_need_ccr_sync_at);
        $this->assertNull($newLoan->unset_need_ccr_sync_at);
        $this->assertEquals(0, $newLoan->ccr_finished);
        $this->assertNull($newLoan->ccr_finished_at);

        //client values should change
        $this->assertEquals(1, $newClient->need_ccr_sync);
        $this->assertEquals(now()->format('Y-m-d'), Carbon::parse($newClient->set_need_ccr_sync_at)->format('Y-m-d'));

        //resetting values and changing address
        $newClient->need_ccr_sync = 0;
        $newClient->set_need_ccr_sync_at = null;
        $newClient->save();
        $dto->relationDto->addressDtoArr = [new AddressDto(1, 'new address','44634', 'current')];
        $newClient = app(SaveEditedClientDataAction::class)->execute($dto)->dbModel();
        $this->assertEquals(1, $newClient->need_ccr_sync);
        $this->assertEquals(now()->format('Y-m-d'), Carbon::parse($newClient->set_need_ccr_sync_at)->format('Y-m-d'));

        //resetting values and changing NAME
        $newClient->need_ccr_sync = 0;
        $newClient->set_need_ccr_sync_at = null;
        $newClient->save();
        $dto->first_name = 'Newname';
        $newClient = app(SaveEditedClientDataAction::class)->execute($dto)->dbModel();
        $this->assertEquals(1, $newClient->need_ccr_sync);
        $this->assertEquals(now()->format('Y-m-d'), Carbon::parse($newClient->set_need_ccr_sync_at)->format('Y-m-d'));
    }

}
