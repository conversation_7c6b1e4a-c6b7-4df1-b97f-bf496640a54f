<?php

namespace Modules\Sales\Tests\Acceptance;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\Approve\Application\Action\ProcessLoanAction;
use Modules\Common\Database\Seeders\Test\ActiveInstallmentsSeeder;
use Modules\Common\Database\Seeders\Test\ActiveLoanSeeder;
use Modules\Common\Database\Seeders\Test\ProcessedLoanSeeder;
use Modules\Common\Enums\Payment\PaymentMethodEnum;
use Modules\Common\Helpers\TestHelpers\LoanSteps;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\Loan;
use Modules\Common\Models\LoanHistory;
use Modules\Common\Models\LoanRefinance;
use Modules\Common\Models\LoanStatus;
use Modules\Common\Models\Office;
use Modules\Sales\Application\Actions\ExistingClientCreateLoanAction;
use Modules\Sales\Application\Actions\UpdateLoanFromClientCardAction;
use Modules\Sales\Domain\Exceptions\NewLoanNotCoveringRefinance;
use Modules\Sales\Http\Dto\LoanDto;
use Tests\TestCase;

class ChangeExistingRefinancingLoanTest  extends TestCase
{
    use DatabaseTransactions;

    const BANK_LOAN_PRINCIPAL = 40000;
    const CASH_LOAN_PRINCIPAL = 60000;

    public function testChangeAllForOnlineLoan()
    {
        $this->actingAs(Administrator::find(Administrator::DEFAULT_ADMINISTRATOR_ID));
        $bankLoan = LoanSteps::createNewClientAndBankLoan(self::BANK_LOAN_PRINCIPAL);
        $bankLoan = LoanSteps::signLoan($bankLoan);
        $bankLoan = LoanSteps::approveLoan($bankLoan);
        $bankLoan =  LoanSteps::activateBankLoan($bankLoan);

        $dto = new LoanDto(
            null,
            Office::OFFICE_ID_VURBICA,
            Administrator::DEFAULT_ADMINISTRATOR_ID,
            PaymentMethodEnum::CASH->id(),
            3,
            self::CASH_LOAN_PRINCIPAL,
            30,
            0,
            [$bankLoan->getKey()],
            null,
            null,
            '************',
            'create_test_browser',
        );
        $action = app(ExistingClientCreateLoanAction::class);
        $action->execute($bankLoan->client, $dto);
        $cashLoan = app(ProcessLoanAction::class)->execute($action->getDbLoan())->dbModel();
        $this->assertEquals(20000.00, $cashLoan->amount_rest);
        $this->assertCount(1, $cashLoan->refinancing);
        $this->assertCount(1, $cashLoan->refresh()->refinancing);
        $this->assertEquals($bankLoan->getKey(), $cashLoan->refresh()->refinancing->first()?->getKey());

        /***************************UPDATING*******************************/
        $dto = new LoanDto(
            $cashLoan->getKey(),
            Office::OFFICE_ID_VURBICA,
            Administrator::DEFAULT_ADMINISTRATOR_ID,
            PaymentMethodEnum::CASH->id(),
            4,
            self::CASH_LOAN_PRINCIPAL,
            90,
            0,
            [],
            null,
            null,
            '************',
            'create_test_browser',
        );
        app(UpdateLoanFromClientCardAction::class)->execute($dto)[0];
        $this->assertCount(0, LoanRefinance::all());
        $this->assertCount(0, $cashLoan->refresh()->refinancing);

    }

    public function testDecreaseSumBelowRefinancing()
    {
        $this->actingAs(Administrator::find(Administrator::DEFAULT_ADMINISTRATOR_ID));
        $bankLoan = LoanSteps::createNewClientAndBankLoan(self::BANK_LOAN_PRINCIPAL);
        $bankLoan = LoanSteps::signLoan($bankLoan);
        $bankLoan = LoanSteps::approveLoan($bankLoan);
        $bankLoan =  LoanSteps::activateBankLoan($bankLoan);

        $dto = new LoanDto(
            null,
            Office::OFFICE_ID_VURBICA,
            Administrator::DEFAULT_ADMINISTRATOR_ID,
            PaymentMethodEnum::CASH->id(),
            3,
            self::CASH_LOAN_PRINCIPAL,
            30,
            0,
            [$bankLoan->getKey()],
            null,
            null,
            '************',
            'create_test_browser',
        );
        $action = app(ExistingClientCreateLoanAction::class);
        $action->execute($bankLoan->client, $dto);
        $cashLoan = app(ProcessLoanAction::class)->execute($action->getDbLoan())->dbModel();
        $this->assertEquals(20000.00, $cashLoan->amount_rest);
        $this->assertCount(1, $cashLoan->refinancing);
        $this->assertCount(1, $cashLoan->refresh()->refinancing);
        $this->assertEquals($bankLoan->getKey(), $cashLoan->refresh()->refinancing->first()?->getKey());

        /***************************UPDATING*******************************/
        $dto = new LoanDto(
            $cashLoan->getKey(),
            Office::OFFICE_ID_VURBICA,
            Administrator::DEFAULT_ADMINISTRATOR_ID,
            PaymentMethodEnum::CASH->id(),
            4,
            self::BANK_LOAN_PRINCIPAL-1,
            90,
            0,
            [$bankLoan->getKey()],
            null,
            null,
            '************',
            'create_test_browser',
        );
        $this->expectException(NewLoanNotCoveringRefinance::class);
        app(UpdateLoanFromClientCardAction::class)->execute($dto)[0];

    }
}
