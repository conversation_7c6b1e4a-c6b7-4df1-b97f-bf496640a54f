<?php

namespace Modules\Sales\Tests\Acceptance;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\DB;
use Modules\Common\Database\Seeders\Test\ActiveInstallmentsSeeder;
use Modules\Common\Database\Seeders\Test\ActiveLoanSeeder;
use Modules\Common\Database\Seeders\Test\AllSteps\AfterLoanCreationSeeder;
use Modules\Common\Database\Seeders\Test\ClientRelationsSeeder;
use Modules\Common\Database\Seeders\Test\ClientSeeder;
use Modules\Common\Database\Seeders\Test\RefinancedInstallmentsSeeder;
use Modules\Common\Database\Seeders\Test\RefinancedLoanSeeder;
use Modules\Common\Enums\Payment\PaymentMethodEnum;
use Modules\Common\Enums\SettingsEnum;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\Loan;
use Modules\Common\Models\LoanEmail;
use Modules\Common\Models\LoanIdCard;
use Modules\Common\Models\LoanRefinance;
use Modules\Common\Models\Representor;
use Modules\Common\Providers\TestDataProvider;
use Modules\Sales\Http\Dto\ClientDto;
use Tests\TestCase;

class StoreNewApplicationTest extends TestCase
{
    use DatabaseTransactions;

    public function testCompanyCashNormalApplication()
    {
        $this->actingAs(Administrator::find(2));
        // just to have a link
        $unusedVariable = '<a href="../../../../storage/example_jsons/physicalOfficeNotSelfApproveCash.json"></a>';
        $data = TestDataProvider::get('physicalOfficeNotSelfApproveCash');
        $response = $this->post(route('sales.saveNewApplication'), $data);
        /** @var Loan $loan */
        $loan = Loan::orderBy('created_at', 'DESC')->first();
        $response->assertStatus(200)->assertJson([
            "success" => true,
            "modalSelector" => "#newAppPhysicalOffice",
            "loanId" => $loan->getKey(),
            "printDocs" => "http://localhost:8000/head/clientCard/print-documents/doc_pack_new_app/" . $loan->getKey(),
            "href" => "http://localhost:8000/head/clientCard/" . $loan->client_id . "/" . $loan->getKey(),
        ]);
        //client
        $client = $loan->client;
        $expectedClient = AfterLoanCreationSeeder::client();
        $this->assertNotNull($client);
        $this->assertEquals($expectedClient['pin'], $client->pin);
        $this->assertEquals($expectedClient['phone'], $client->phone);
        $this->assertEquals($expectedClient['idcard_number'], $client->idcard_number);
        //client_actual_status
        $expectedClientStats = AfterLoanCreationSeeder::clientActualStats();
        $this->assertNotNull($client->clientActualStats);
        $this->assertEquals($expectedClientStats['applications_count'], $client->clientActualStats->applications_count);
        //client_address
        $expectedClientAddress = AfterLoanCreationSeeder::clientAddress();
        $this->assertCount(count($expectedClientAddress), $client->addresses);
        $this->assertEquals($expectedClientAddress[0]['type'], $client->addresses[1]->type);
        $this->assertEquals($expectedClientAddress[1]['type'], $client->addresses[0]->type);
        //client_email
        $expectedClientEmail = AfterLoanCreationSeeder::clientEmail();
        $this->assertCount(count($expectedClientEmail), $client->clientEmails);
        //client_history
//        $expectedClientHistory = AfterLoanCreationSeeder::clientHistory();
//        $this->assertEqualsCanonicalizing(
//            array_column($expectedClientHistory, 'field'),
//            $client->clientHistory->pluck('field')->toArray()
//        );
        //client_idcard
        $expectedClientIdCard = AfterLoanCreationSeeder::clientIdCard();
        $this->assertCount(count($expectedClientIdCard), $client->clientIdCards);
        //client_name
        $expectedClientName = AfterLoanCreationSeeder::clientName();
        $this->assertCount(count($expectedClientName), $client->clientNames);
        //client_office
        $expectedClientOffice = AfterLoanCreationSeeder::clientOffice();
        $this->assertCount(count($expectedClientOffice), $client->clientOffices());
        //client_phone
        $expectedClientPhone = AfterLoanCreationSeeder::clientPhone();
        $this->assertCount(count($expectedClientPhone), $client->clientPhones);
        //client_picture
        $expectedClientPicture = AfterLoanCreationSeeder::clientPicture();
        $this->assertCount(count($expectedClientPicture), $client->clientPictures);
        //client_representor
        $expectedClientRepresentative = AfterLoanCreationSeeder::clientRepresentative();
        $this->assertCount(count($expectedClientRepresentative), $client->clientRepresentors);
        //representor
        $expectedRepresentative = AfterLoanCreationSeeder::representative();
        $representatives = Representor::all();
        $this->assertCount(count($expectedRepresentative), $representatives);
        //loan
        $expectedLoan = AfterLoanCreationSeeder::loan()[0];
        $this->assertEquals($expectedLoan['amount_requested'], $loan->amount_requested);
        $this->assertEquals($expectedLoan['product_id'], $loan->product_id);
        $this->assertEquals($expectedLoan['period_requested'], $loan->period_requested);
        $this->assertEquals($expectedLoan['created_by'], $loan->created_by);
        $this->assertEquals($expectedLoan['payment_method_id'], $loan->payment_method_id);
        //installment
        $installments = $loan->installments;
        $expectedInstallment = AfterLoanCreationSeeder::installment();
        $this->assertCount(count($expectedInstallment), $installments);
        //loan_actual_stats
        $loanActualStats = $loan->loanActualStats;
        $expectedLoanActualStats = AfterLoanCreationSeeder::loanActualStats()[0];
        $this->assertEquals($expectedLoanActualStats['profit'], $loanActualStats->profit);
        //loan_address
        $loanAddresses = $loan->addresses;
        $expectedLoanAddress = AfterLoanCreationSeeder::loanAddress();
        $this->assertCount(count($expectedLoanAddress), $loanAddresses);
        //loan_client_name
        $loanClientName = $loan->clientName[0];
        $expectedLoanClientName = AfterLoanCreationSeeder::loanClientName()[0];
        $this->assertEquals($expectedLoanClientName['last'], $loanClientName->last);
        //loan_contact_actual
        $loanContacts = $loan->loanContacts;
        $expectedLoanContactActual = AfterLoanCreationSeeder::loanContactActual();
        $this->assertCount(count($expectedLoanContactActual), $loanContacts);
        //loan_email
        $loanEmails = LoanEmail::all();
        $expectedLoanEmail = AfterLoanCreationSeeder::loanEmail();
        $this->assertCount(count($expectedLoanEmail), $loanEmails);
        //loan_history
//        $expectedLoanHistory = AfterLoanCreationSeeder::loanHistory();
//        $this->assertEqualsCanonicalizing(
//            array_column($expectedLoanHistory, 'field'),
//            $loan->loanHistory->pluck('field')->toArray()
//        );
        //loan_idcard
        $loanIdCard = LoanIdCard::all()[0];
        $expectedLoanIdCard = AfterLoanCreationSeeder::loanIdCard();
        $this->assertEquals($expectedLoanIdCard[0]['last'], $loanIdCard->last);
        //loan_meta
        $loanMeta = $loan->meta;
        $expectedLoanMeta = AfterLoanCreationSeeder::loanMeta();
        $this->assertCount(count($expectedLoanMeta), $loanMeta);
        //loan_phone
        $loanPhones = $loan->phones;
        $expectedLoanPhones = AfterLoanCreationSeeder::loanPhone();
        $this->assertCount(count($expectedLoanPhones), $loanPhones);
        //loan_product_setting
        $loanProductSetting = $loan->loanProductSetting;
        $expectedLoanProductSetting = AfterLoanCreationSeeder::loanProductSetting()[0];
        $this->assertNotNull($loanProductSetting);
        $this->assertEquals($expectedLoanProductSetting['product_id'], $loanProductSetting->product_id);
        //loan_status_history
        $loanStatusHistory = $loan->loanStatusHistory;
        $expectedLoanStatusHistory = AfterLoanCreationSeeder::loanStatusHistory();
        $this->assertCount(count($expectedLoanStatusHistory), $loanStatusHistory);
        //notification_setting
        $notificationSettings = $client->notificationSettings;
        $expectedNotificationSetting = AfterLoanCreationSeeder::notificationSetting();
        $this->assertCount(count($expectedNotificationSetting), $notificationSettings);
    }


    public function testIndividualBankNormalApplication()
    {
        $this->actingAs(Administrator::find(2));
        $data = TestDataProvider::get('physicalOfficeNotSelfApproveBank');
        $response = $this->post(route('sales.saveNewApplication'), $data);
        /** @var Loan $loan */
        $loan = Loan::orderBy('created_at', 'DESC')->first();
        $response->assertStatus(200)->assertJson([
            "success" => true,
            "modalSelector" => "#newAppPhysicalOffice",
            "loanId" => $loan->getKey(),
            "printDocs" => "http://localhost:8000/head/clientCard/print-documents/doc_pack_new_app/" . $loan->getKey(),
            "href" => "http://localhost:8000/head/clientCard/" . $loan->client_id . "/" . $loan->getKey(),
        ]);
        //client
        $client = $loan->client;
        $expectedClient = AfterLoanCreationSeeder::client();
        $this->assertNotNull($client);
        $this->assertEquals($expectedClient['pin'], $client->pin);
        $this->assertEquals($expectedClient['phone'], $client->phone);
        $this->assertEquals($expectedClient['idcard_number'], $client->idcard_number);
        //client_history
        $expectedClientHistory = AfterLoanCreationSeeder::clientHistory();
        $expected = array_merge(
            array_column($expectedClientHistory, 'field'),
            ['client.first_name_latin', 'client.gender']
        );
        $actual = $client->clientHistory->pluck('field')->toArray();
        $this->assertEqualsCanonicalizing(
            sort($expected),
            sort($actual)
        );
        //client_representor
        $this->assertCount(0, $client->clientRepresentors);
        //loan
        $expectedLoan = AfterLoanCreationSeeder::loan()[0];
        $this->assertEquals($expectedLoan['amount_requested'], $loan->amount_requested);
        $this->assertEquals($expectedLoan['product_id'], $loan->product_id);
        $this->assertEquals($expectedLoan['period_requested'], $loan->period_requested);
        $this->assertEquals($expectedLoan['created_by'], $loan->created_by);
        $this->assertEquals(PaymentMethodEnum::BANK->id(), $loan->payment_method_id);
        //installment
        $installments = $loan->installments;
        $expectedInstallment = AfterLoanCreationSeeder::installment();
        $this->assertCount(count($expectedInstallment), $installments);
        //loan_actual_stats
        $loanActualStats = $loan->loanActualStats;
        $expectedLoanActualStats = AfterLoanCreationSeeder::loanActualStats()[0];
        $this->assertEquals($expectedLoanActualStats['profit'], $loanActualStats->profit);
        //loan_history
        $expectedLoanHistory = AfterLoanCreationSeeder::loanHistory();
        $expected = array_merge(
            array_column($expectedLoanHistory, 'field'),
            ['loan_bank_account.client_bank_account_id']
        );
        $actual = $loan->loanHistory->pluck('field')->toArray();
        $this->assertEqualsCanonicalizing(
            sort($expected),
            sort($actual)
        );
    }

    public function testAdminDiscountBiggerThanClientDiscount()
    {
        $this->seed(ClientRelationsSeeder::class);
        $this->actingAs(Administrator::find(2));
        DB::table('administrator_setting')->insert([
            'administrator_id' => 2,
            'setting_key' => SettingsEnum::max_discount_percent_administrator,
            'value' => 30
        ]);
        DB::table('client_discount_actual')->insert([
            'client_id' => ClientSeeder::CLIENT_ID,
            'product_id' => 3,
            'valid_from' => now()->subHour(),
            'valid_till' => now()->addHour(),
            'percent' => 40
        ]);
        $data = TestDataProvider::get('physicalOfficeNotSelfApproveBank');
        $data['client_idcard']['pin'] = ClientSeeder::PIN;
        $data['loan']['discount_percent'] = 40;
        $response = $this->post(route('sales.saveNewApplication'), $data);
        /** @var Loan $loan */
        $loan = Loan::orderBy('created_at', 'DESC')->first();
        $response->assertStatus(200)->assertJson([
            "success" => true,
            "modalSelector" => "#newAppPhysicalOffice",
            "loanId" => $loan->getKey(),
            "printDocs" => "http://localhost:8000/head/clientCard/print-documents/doc_pack_new_app/" . $loan->getKey(),
            "href" => "http://localhost:8000/head/clientCard/" . $loan->client_id . "/" . $loan->getKey(),
        ]);
        $this->assertEquals(40, $loan->discount_percent);
    }

    public function testClientDiscountBiggerThanAdminDiscount()
    {
        $this->seed(ClientRelationsSeeder::class);
        $this->actingAs(Administrator::find(2));
        DB::table('administrator_setting')->insert([
            'administrator_id' => 2,
            'setting_key' => SettingsEnum::max_discount_percent_administrator,
            'value' => 50
        ]);
        DB::table('client_discount_actual')->insert([
            'client_id' => ClientSeeder::CLIENT_ID,
            'product_id' => 3,
            'valid_from' => now()->subHour(),
            'valid_till' => now()->addHour(),
            'percent' => 20
        ]);
        $data = TestDataProvider::get('physicalOfficeNotSelfApproveBank');
        $data['client_idcard']['pin'] = ClientSeeder::PIN;
        $data['loan']['discount_percent'] = 30;
        $response = $this->post(route('sales.saveNewApplication'), $data);
        /** @var Loan $loan */
        $loan = Loan::orderBy('created_at', 'DESC')->first();
        $response->assertStatus(200)->assertJson([
            "success" => true,
            "modalSelector" => "#newAppPhysicalOffice",
            "loanId" => $loan->getKey(),
            "printDocs" => "http://localhost:8000/head/clientCard/print-documents/doc_pack_new_app/" . $loan->getKey(),
            "href" => "http://localhost:8000/head/clientCard/" . $loan->client_id . "/" . $loan->getKey(),
        ]);
        $this->assertEquals(30, $loan->discount_percent);
    }

    public function testDiscountIsBiggerThanLimit()
    {
        $this->seed(ClientRelationsSeeder::class);
        $this->actingAs(Administrator::find(2));
        DB::table('administrator_setting')->insert([
            'administrator_id' => 2,
            'setting_key' => SettingsEnum::max_discount_percent_administrator,
            'value' => 30
        ]);
        DB::table('client_discount_actual')->insert([
            'client_id' => ClientSeeder::CLIENT_ID,
            'product_id' => 3,
            'valid_from' => now()->subHour(),
            'valid_till' => now()->addHour(),
            'percent' => 20
        ]);
        $data = TestDataProvider::get('physicalOfficeNotSelfApproveBank');
        $data['client_idcard']['pin'] = ClientSeeder::PIN;
        $data['loan']['discount_percent'] = 40;
        //run
        $response = $this->post(route('sales.saveNewApplication'), $data);
        //TODO: why message is null? NewLoan::setDiscountPercent throws exception!
        $response->assertJson([
            "success" => false,
            //"message" => "discount of 40 is above limit of 30",
            "modalSelector" => "#errorModal"
        ]);
    }

    public function testRefinancingTwoLoans()
    {
        $this->seed([
            ActiveInstallmentsSeeder::class,
            RefinancedInstallmentsSeeder::class
        ]);
        $this->actingAs(Administrator::find(2));
        /** @var Loan $refinancedLoan1 */
        $refinancedLoan1 = Loan::find(ActiveLoanSeeder::LOAN_ID);
        $refinancedLoan1->amount_requested = $refinancedLoan1->amount_approved = 500;
        $refinancedLoan1->save();
        $refinancedLoan2 = Loan::find(RefinancedLoanSeeder::LOAN_ID);
        $refinancedLoan2->amount_requested = $refinancedLoan2->amount_approved = 300;
        $refinancedLoan2->save();
        /** @var ClientDto $dto */
        $data = TestDataProvider::get('physicalOfficeNotSelfApproveBank');
        $data['client_idcard']['pin'] = ClientSeeder::PIN;
        $data['loan']['loan_sum'] = 1000;
        $data['refinanced_loans'] = [ActiveLoanSeeder::LOAN_ID, RefinancedLoanSeeder::LOAN_ID];
        $response = $this->post(route('sales.saveNewApplication'), $data);
        /** @var Loan $loan */
        $loan = Loan::orderBy('created_at', 'DESC')->first();
        $response->assertStatus(200)->assertJson([
            "success" => true,
            "modalSelector" => "#newAppPhysicalOffice",
            "loanId" => $loan->getKey(),
            "printDocs" => "http://localhost:8000/head/clientCard/print-documents/doc_pack_new_app/" . $loan->getKey(),
            "href" => "http://localhost:8000/head/clientCard/" . $loan->client_id . "/" . $loan->getKey(),
        ]);

        $rel1 = LoanRefinance::where(['refinanced_loan_id' => ActiveLoanSeeder::LOAN_ID])->first();
        $this->assertNotNull($rel1);
        $this->assertEquals($loan->getKey(), $rel1->refinancing_loan_id);
        $rel2 = LoanRefinance::where(['refinanced_loan_id' => RefinancedLoanSeeder::LOAN_ID])->first();
        $this->assertNotNull($rel2);
        $this->assertEquals($loan->getKey(), $rel2->refinancing_loan_id);
    }

    public function testRefinancingOneOfTwoLoans()
    {
        $this->seed([
            ActiveInstallmentsSeeder::class,
            RefinancedInstallmentsSeeder::class
        ]);
        $this->actingAs(Administrator::find(2));
        /** @var Loan $refinancedLoan1 */
        $refinancedLoan1 = Loan::find(ActiveLoanSeeder::LOAN_ID);
        $refinancedLoan1->amount_requested = $refinancedLoan1->amount_approved = 500;
        $refinancedLoan1->save();
        $refinancedLoan2 = Loan::find(RefinancedLoanSeeder::LOAN_ID);
        $refinancedLoan2->amount_requested = $refinancedLoan2->amount_approved = 300;
        $refinancedLoan2->save();
        /** @var ClientDto $dto */
        $data = TestDataProvider::get('physicalOfficeNotSelfApproveBank');
        $data['client_idcard']['pin'] = ClientSeeder::PIN;
        $data['loan']['loan_sum'] = 1000;
        $data['refinanced_loans'] = [ActiveLoanSeeder::LOAN_ID];
        $response = $this->post(route('sales.saveNewApplication'), $data);
        /** @var Loan $loan */
        $loan = Loan::orderBy('created_at', 'DESC')->first();
        $response->assertStatus(200)->assertJson([
            "success" => true,
            "modalSelector" => "#newAppPhysicalOffice",
            "loanId" => $loan->getKey(),
            "printDocs" => "http://localhost:8000/head/clientCard/print-documents/doc_pack_new_app/" . $loan->getKey(),
            "href" => "http://localhost:8000/head/clientCard/" . $loan->client_id . "/" . $loan->getKey(),
        ]);

        $rel1 = LoanRefinance::where(['refinanced_loan_id' => ActiveLoanSeeder::LOAN_ID])->first();
        $this->assertNotNull($rel1);
        $this->assertEquals($loan->getKey(), $rel1->refinancing_loan_id);
        $rel2 = LoanRefinance::where(['refinanced_loan_id' => RefinancedLoanSeeder::LOAN_ID])->first();
        $this->assertNull($rel2);
    }

}
