<?php

namespace Modules\Sales\Tests\Acceptance;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Carbon;
use Modules\Approve\Application\Action\ApproveLoanAction;
use Modules\Approve\Application\Action\ProcessLoanAction;
use Modules\Approve\Presentation\Dto\DecisionDto;
use Modules\Collect\Domain\Entities\LoanForExtension;
use Modules\Collect\Application\Action\MassMoveLoanBucketAction;
use Modules\Collect\Http\Dto\LoanBucketDto;
use Modules\Common\Enums\BucketEnum;
use Modules\Common\Models\ApproveDecision;
use Modules\Common\Models\ApproveDecisionReason;
use Modules\Common\Models\Client;
use Modules\Common\Models\LoanStatus;
use Modules\Common\Models\Tax;
use Modules\Common\Providers\TestDataProvider;
use Modules\Head\Console\CcrReportOut;
use Modules\Common\Models\CcrReportOut as DbModel;
use Modules\Head\Services\LoanService;
use Modules\Payments\Application\Actions\Task\ConfirmEasyPaySendingAction;
use Modules\Sales\Application\Actions\ExistingClientCreateLoanAction;
use Modules\Sales\Application\Actions\NewClientNoLoanAction;
use Modules\Sales\Application\Actions\SaveEditedClientDataAction;
use Modules\Sales\Application\Actions\SignLoanAction;
use Modules\Sales\Http\Dto\ClientDto;
use Modules\Sales\Http\Middleware\DtoSerializerNewClientLoan;
use Tests\TestCase;

class CcrFlagsTest extends TestCase
{
    use DatabaseTransactions;

    public function setUp(): void
    {
        parent::setUp();
    }

    public function testHappyPath()
    {
        $clientFlags = [
            'registered_in_ccr',
            'need_ccr_sync',
            'registered_in_ccr_at',
            'set_need_ccr_sync_at',
            'unset_need_ccr_sync_at'
        ];
        $loanFlags = [
            'registered_in_ccr',
            'need_ccr_sync',
            'registered_in_ccr_at',
            'set_need_ccr_sync_at',
            'unset_need_ccr_sync_at',
            'ccr_finished',
            'ccr_finished_at',
        ];
        $todayString = now()->format('Y-m-d');
        /** @var ClientDto $clientDto */
        $clientDto = TestDataProvider::get(
            'applicationRequest1',
            DtoSerializerNewClientLoan::class,
            'createClientDto'
        );
        $loanDto = $clientDto->relationDto->loanDto;
        $clientDto->relationDto->loanDto = null;
        /******************************A) when we create client, all these fields are null **************/
        $client = app(NewClientNoLoanAction::class)->execute($clientDto)->dbModel();
        foreach ($clientFlags as $attribute){
            $this->assertNull($client->$attribute);
        }
        /********************** C) CREATE LOAN **********************/
        $loan = app(ExistingClientCreateLoanAction::class)->execute($client, $loanDto)->dbLoan();
        foreach ($clientFlags as $attribute){
            $this->assertNull($loan->client->$attribute);
        }
        foreach ($loanFlags as $attribute){
            $this->assertNull($loan->$attribute);
        }
        /********************** SIGN LOAN **********************/
        $loan = app(SignLoanAction::class)->execute($loan)->dbModel();
        foreach ($clientFlags as $attribute){
            $this->assertNull($loan->client->$attribute);
        }
        foreach ($loanFlags as $attribute){
            $this->assertNull($loan->$attribute);
        }

        /********************** APPROVE LOAN **********************/
        $loan = app(ProcessLoanAction::class)->execute($loan)->dbModel();
        $loan = app(ApproveLoanAction::class)->execute(new DecisionDto(
            $loan->getKey(),
            1,
            ApproveDecision::APPROVE_DECISION_APPROVED,
            ApproveDecisionReason::APPROVE_DECISION_REASON_OTHER,
            '',
            null
        ));
        foreach ($clientFlags as $attribute){
            $this->assertNull($loan->client->$attribute);
        }
        foreach ($loanFlags as $attribute){
            $this->assertNull($loan->$attribute);
        }

        /********************** ACTIVATE LOAN **********************/
        $payment = app(ConfirmEasyPaySendingAction::class)->execute($loan->payments->first());
        $loan->refresh();
        $client->refresh();
        $this->assertEquals(LoanStatus::ACTIVE_STATUS_ID, $loan->loan_status_id);
        $this->assertTrue(0 === $client->registered_in_ccr);
        $this->assertTrue(0 === $loan->registered_in_ccr);
        $this->assertTrue(0 === $loan->ccr_finished);
        $this->assertNull($loan->ccr_finished_at);
        $this->assertTrue(0 === $client->need_ccr_sync);

        /**************************B) we run command: php artisan script:ccr-report-out:generate borr *********************/
        $this->artisan(CcrReportOut::name(), ['type'=>DbModel::TYPE_CLIENT])->assertSuccessful();

        $this->assertEquals(1, $client->refresh()->registered_in_ccr);
        $this->assertTrue(0 === $client->need_ccr_sync);
        $this->assertEquals($todayString, Carbon::parse($client->registered_in_ccr_at)->format('Y-m-d'));

        /******************************D) we run script: php artisan script:ccr-report-out:generate cred*******************/
        $this->artisan(CcrReportOut::name(), ['type'=>DbModel::TYPE_LOAN])->assertSuccessful();

        /**************************** loan fields become: ****************************/
        $this->assertEquals(1, $loan->refresh()->registered_in_ccr);
        $this->assertTrue(0 === $loan->need_ccr_sync);
        $this->assertEquals($todayString, Carbon::parse($loan->registered_in_ccr_at)->format('Y-m-d'));

        /**************************** E)when we do ExtendLoan: *******************/
        $daysToExtend = 10;
        $extendFeeAmount = app(LoanService::class)->calculateExtendLoanFeeAmount($loan, $daysToExtend);
        $loan = app(LoanForExtension::class)->buildFromExisting($loan)
            ->extend($extendFeeAmount, $daysToExtend)
            ->dbModel();

        /**************************** we set loan fields: *******************/
        $this->assertEquals(1, $loan->need_ccr_sync);
        $this->assertEquals($todayString, Carbon::parse($loan->set_need_ccr_sync_at)->format('Y-m-d'));
        //– just check these fields, we do not change them:
        $this->assertEquals(1, $loan->registered_in_ccr);
        $this->assertEquals($todayString, Carbon::parse($loan->registered_in_ccr_at)->format('Y-m-d'));
        $this->assertTrue(0 === $loan->ccr_finished);
        $this->assertNull($loan->ccr_finished_at);

        /*******************************F) we run script: ********************/
        $this->artisan(CcrReportOut::name(), ['type'=>DbModel::TYPE_LOAN])->assertSuccessful();
        //loan fields become:
        $this->assertTrue(0 === $loan->refresh()->need_ccr_sync);
        $this->assertNull($loan->set_need_ccr_sync_at);
        $this->assertEquals($todayString, Carbon::parse($loan->unset_need_ccr_sync_at)->format('Y-m-d'));
        //– just check these fields, we do not change them:
        $this->assertEquals(1, $loan->registered_in_ccr);
        $this->assertEquals($todayString, Carbon::parse($loan->registered_in_ccr_at)->format('Y-m-d'));
        $this->assertTrue(0 === $loan->ccr_finished);
        $this->assertNull($loan->ccr_finished_at);

        /******* G) repeat point E) & F) to validate that on every loan extension we do it.*********/
        // E)!!!!!!!!!!!!!!!!!!!!!
        $fee = Tax::all()->first();
        $fee->created_at = now()->subDay();
        $fee->save();
        $extendFeeAmount = app(LoanService::class)->calculateExtendLoanFeeAmount($loan, $daysToExtend);
        $loan = app(LoanForExtension::class)->buildFromExisting($loan)
            ->extend($extendFeeAmount, $daysToExtend)
            ->dbModel();

        $this->assertEquals(1, $loan->need_ccr_sync);
        $this->assertEquals($todayString, Carbon::parse($loan->set_need_ccr_sync_at)->format('Y-m-d'));
        $this->assertEquals(1, $loan->registered_in_ccr);
        $this->assertEquals($todayString, Carbon::parse($loan->registered_in_ccr_at)->format('Y-m-d'));
        $this->assertTrue(0 === $loan->ccr_finished);
        $this->assertNull($loan->ccr_finished_at);
        // F)!!!!!!!!!!!!!!!!!!!!!
        $this->artisan(CcrReportOut::name(), ['type'=>DbModel::TYPE_LOAN])->assertSuccessful();
        $this->assertTrue(0 === $loan->refresh()->need_ccr_sync);
        $this->assertNull($loan->set_need_ccr_sync_at);
        $this->assertEquals($todayString, Carbon::parse($loan->unset_need_ccr_sync_at)->format('Y-m-d'));
        $this->assertEquals(1, $loan->registered_in_ccr);
        $this->assertEquals($todayString, Carbon::parse($loan->registered_in_ccr_at)->format('Y-m-d'));
        $this->assertTrue(0 === $loan->ccr_finished);
        $this->assertNull($loan->ccr_finished_at);


        /************H) we change client any of this (separate test for every field)****/
        foreach (Client::CCR_BORR_TRIGER_PROPS as $attribute){
            if($attribute === 'legal_status_code'){
                $clientDto->legal_status_code = Client::LS_CODE_LOCAL_INVD;
            } else if($attribute === 'economy_sector_code'){
                $clientDto->economy_sector_code = Client::ES_CODE_INVD;
            } else if($attribute === 'industry_code'){
                $clientDto->industry_code = Client::IND_CODE_INVD;
            } else {
                $clientDto->$attribute = $clientDto->$attribute . "Ccr";
            }
            $client = app(SaveEditedClientDataAction::class)->execute($clientDto)->dbModel();
            //we set client fields:
            $this->assertEquals(1, $client->need_ccr_sync);
            $this->assertEquals($todayString, Carbon::parse($client->set_need_ccr_sync_at)->format('Y-m-d'));
            //– just check these fields, we do not change them:
            $this->assertEquals(1, $client->registered_in_ccr);
            $this->assertEquals($todayString, Carbon::parse($client->registered_in_ccr_at)->format('Y-m-d'));

            /*************************I) we run script: php artisan script:ccr-report-out:generate borr ************************/
            $this->artisan(CcrReportOut::name(), ['type'=>DbModel::TYPE_CLIENT])->assertSuccessful();
            //client fields become
            $this->assertTrue(0 === $client->refresh()->need_ccr_sync);
            $this->assertNull($client->set_need_ccr_sync_at);
            $this->assertEquals($todayString, Carbon::parse($client->unset_need_ccr_sync_at)->format('Y-m-d'));
            //– just check these fields, we do not change them:
            $this->assertEquals(1, $client->registered_in_ccr);
            $this->assertEquals($todayString, Carbon::parse($client->registered_in_ccr_at)->format('Y-m-d'));
        }

        /***J) we  do loan repayment, so the loan status become REPAID or we do loan status: WRITTEN-OFF**/
        app(MassMoveLoanBucketAction::class)->execute([new LoanBucketDto(
            $loan->getKey(),
            BucketEnum::WRITTEN_OFF->id(),
        'test',
        1,
        2
        )]);

        $this->assertEquals(LoanStatus::WRITTEN_OF_STATUS_ID, $loan->refresh()->loan_status_id);
        $loan->created_at = now()->startOfMonth()->subDay();
        $loan->save();
        //and run script php artisan script:ccr-report-out:generate cucr:
        $this->artisan(CcrReportOut::name(), ['type'=>DbModel::TYPE_MONTHLY])->assertSuccessful();
        //loan fields are:
        $this->assertEquals(1, $loan->refresh()->registered_in_ccr);
        $this->assertTrue(0 === $loan->need_ccr_sync);
        $this->assertEquals($todayString, Carbon::parse($loan->registered_in_ccr_at)->format('Y-m-d'));
        $this->assertNull($loan->set_need_ccr_sync_at);
        $this->assertEquals($todayString, Carbon::parse($loan->unset_need_ccr_sync_at)->format('Y-m-d'));
        $this->assertEquals(1, $loan->ccr_finished);
        $this->assertEquals($todayString, Carbon::parse($loan->ccr_finished_at)->format('Y-m-d'));
    }

}
