<?php

namespace Modules\Sales\Tests\Acceptance;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Carbon;
use Modules\Approve\Application\Action\ApproveLoanAction;
use Modules\Approve\Application\Action\ProcessLoanAction;
use Modules\Approve\Presentation\Dto\DecisionDto;
use Modules\Collect\Application\Action\MassMoveLoanBucketAction;
use Modules\Collect\Http\Dto\LoanBucketDto;
use Modules\Common\Enums\BucketEnum;
use Modules\Common\Enums\Payment\PaymentMethodEnum;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\ApproveDecision;
use Modules\Common\Models\ApproveDecisionReason;
use Modules\Common\Models\Client;
use Modules\Common\Models\LoanStatus;
use Modules\Common\Models\Office;
use Modules\Common\Models\Tax;
use Modules\Common\Providers\TestDataProvider;
use Modules\Head\Console\CcrReportOut;
use Modules\Common\Models\CcrReportOut as DbModel;
use Modules\Head\Services\LoanService;
use Modules\Payments\Application\Actions\Task\ConfirmEasyPaySendingAction;
use Modules\Sales\Application\Actions\ExistingClientCreateLoanAction;
use Modules\Sales\Application\Actions\NewClientNoLoanAction;
use Modules\Sales\Application\Actions\SaveEditedClientDataAction;
use Modules\Sales\Application\Actions\SignLoanAction;
use Modules\Sales\Http\Dto\ClientDto;
use Modules\Sales\Http\Dto\LoanDto;
use Modules\Sales\Http\Middleware\DtoSerializerNewClientLoan;
use Tests\TestCase;

class ClientNewFlagTest extends TestCase
{
    use DatabaseTransactions;

    public function setUp(): void
    {
        parent::setUp();
    }

    public function testHappyPath()
    {

        /** @var ClientDto $clientDto */
        $clientDto = TestDataProvider::get(
            'applicationRequest1',
            DtoSerializerNewClientLoan::class,
            'createClientDto'
        );
        $loanDto = $clientDto->relationDto->loanDto;
        $clientDto->relationDto->loanDto = null;
        /******************************A) when we create new client, new = 1 **************/
        $client = app(NewClientNoLoanAction::class)->execute($clientDto)->dbModel();
        $this->assertEquals(1, $client->new);

        /********************** when we create a loan. client.new = 1 **********************/
        $loan = app(ExistingClientCreateLoanAction::class)->execute($client, $loanDto)->dbLoan();
        $this->assertEquals(1, $client->refresh()->new);


        /********************** when we approve a loan. client.new = 1 **********************/
        $loan = app(SignLoanAction::class)->execute($loan)->dbModel();
        $loan = app(ProcessLoanAction::class)->execute($loan)->dbModel();
        $loan = app(ApproveLoanAction::class)->execute(new DecisionDto(
            $loan->getKey(),
            1,
            ApproveDecision::APPROVE_DECISION_APPROVED,
            ApproveDecisionReason::APPROVE_DECISION_REASON_OTHER,
            '',
            null
        ));
        $this->assertEquals(1, $client->refresh()->new);

        /********************** when we activate a loan. client.new = 0 **********************/
        app(ConfirmEasyPaySendingAction::class)->execute($loan->payments->first());
        $this->assertTrue(0 === $client->refresh()->new);


        /***when we create next loan for a client, no matter status of loan, client.new = 0;**/
        $dto = new LoanDto(
            null,
            Office::OFFICE_ID_NOVI_PAZAR_1,
            Administrator::DEFAULT_ADMINISTRATOR_ID,
            PaymentMethodEnum::CASH->id(),
            3,
            30000,
            30,
            0,
            [],
            null,
            null,
            '85.254.75.95',
            'create_test_browser',
        );
        app(ExistingClientCreateLoanAction::class)->execute($client, $dto);
        $this->assertTrue(0 === $client->refresh()->new);

    }

}
