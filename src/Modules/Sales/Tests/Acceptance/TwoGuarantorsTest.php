<?php

namespace Modules\Sales\Tests\Acceptance;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\Common\Providers\TestDataProvider;
use Modules\Sales\Application\Actions\NewAppAction;
use Modules\Sales\Http\Dto\ClientDto;
use Modules\Sales\Http\Middleware\DtoSerializerNewClientLoan;
use Tests\TestCase;

class TwoGuarantorsTest extends TestCase
{
    use DatabaseTransactions;

    //vendor/bin/phpunit Modules/Collect/Tests/Acceptance/TwoGuarantorsTest.php
    public function testReceivingCashFullManualPayment()
    {
        /** @var ClientDto $clientDto */
        $clientDto = TestDataProvider::get(
            'individual_with_guarantors_installments',
            DtoSerializerNewClientLoan::class,
            'createClientDto'
        );

        $client = app(NewAppAction::class)->execute($clientDto);
        $loan = $client->dbLoan();
        $this->assertCount(2, $loan->loanGuarants()->count());
    }
}
