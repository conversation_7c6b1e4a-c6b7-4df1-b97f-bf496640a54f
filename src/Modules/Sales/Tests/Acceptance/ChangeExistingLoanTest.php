<?php

namespace Modules\Sales\Tests\Acceptance;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\Common\Database\Seeders\Test\ActiveLoanSeeder;
use Modules\Common\Database\Seeders\Test\ProcessedLoanSeeder;
use Modules\Common\Enums\Payment\PaymentMethodEnum;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\Loan;
use Modules\Common\Models\LoanHistory;
use Modules\Common\Models\LoanStatus;
use Tests\TestCase;

class ChangeExistingLoanTest  extends TestCase
{
    use DatabaseTransactions;

    public function testChangeAllForOnlineLoan()
    {
        $this->actingAs(Administrator::find(Administrator::DEFAULT_ADMINISTRATOR_ID));
        $this->seed([
            ProcessedLoanSeeder::class,
            ActiveLoanSeeder::class
        ]);
        /** @var Loan $dbLoan */
        $dbLoan = Loan::find(ProcessedLoanSeeder::LOAN_ID);
        /** @var Loan $refLoan */
        $refLoan = Loan::find(ActiveLoanSeeder::LOAN_ID);
        $debt = $refLoan->getRegularRepaymentDebtDb();
        //checking initial values
        $this->assertEquals(1, $dbLoan->product_id);
        $this->assertEquals(30, $dbLoan->period_requested);
        $this->assertEquals(1000, $dbLoan->amount_requested);
        $this->assertEquals(10, $dbLoan->discount_percent);
        $this->assertEquals(PaymentMethodEnum::BANK->id(), $dbLoan->payment_method_id);
        $this->assertCount(0, $dbLoan->refinancing);
        $this->assertEquals(LoanStatus::PROCESSING_STATUS_ID, $dbLoan->loan_status_id);

        //Setting completely different values
        $newData['refinanced_loans'] = [ActiveLoanSeeder::LOAN_ID];
        $newData['loan'] = [
            'loan_id' => ProcessedLoanSeeder::LOAN_ID,
            'product_id' => 3,
            'loan_period' => 7,
            'loan_sum' => 2000,
            'discount_percent' => 20,
            'office_id' => 2,
            'payment_method' => PaymentMethodEnum::CASH->id(),
        ];

        //Submitting via POST
        $response = $this->post(
            route('sales.sale-loan.update-loan'),
            $newData
        );
        //checking the HTTP response
        $response->assertStatus(200)->assertJson([
            "status" => true,
            "loan" => ["loan_id" => ProcessedLoanSeeder::LOAN_ID],
            "params" => [
                "productName" => "До заплата",
                "amountApproved" => $newData['loan']['loan_sum']*100,
                "loanPeriodParam" => "7 дни",
                "installmentsCountParam" => "1 вноска",
                "firstInstallmentAmount" => "2085.80 лв",
                "paymentMethod" => "В брой",
                "officeName" => "офис Нови Пазар",
            ]
        ]);
        //checking changes in the loan in database
        $dbLoan->refresh();
        $this->assertEquals($newData['loan']['product_id'], $dbLoan->product_id);
        $this->assertEquals($newData['loan']['loan_period'], $dbLoan->period_requested);
        $this->assertEquals($newData['loan']['discount_percent'], $dbLoan->discount_percent);
        $this->assertEquals($newData['loan']['payment_method'], $dbLoan->payment_method_id);
        $this->assertCount(count($newData['refinanced_loans']), $dbLoan->refinancing);
        $this->assertEquals($newData['loan']['loan_sum']*100, $dbLoan->amount_requested);
        //check that loan status was reverted to signed
        $this->assertEquals(LoanStatus::SIGNED_STATUS_ID, $dbLoan->loan_status_id);
        //checking loan status history
//        $historyArr = LoanHistory::where(['loan_id'=>ProcessedLoanSeeder::LOAN_ID])->get()->keyBy('field');
//        $this->assertCount(17, $historyArr);
//        $this->assertEquals($newData['loan']['product_id'], $historyArr['loan.product_id']->value_to);
//        $this->assertEquals(2, $historyArr['loan.loan_type_id']->value_to);
//        $this->assertEquals(20, $historyArr['loan.discount_percent']->value_to);
//        $this->assertEquals(200000, $historyArr['loan.amount_requested']->value_to);
//        $this->assertEquals(1, $historyArr['loan.installments_requested']->value_to);
//        $this->assertEquals(7, $historyArr['loan.period_requested']->value_to);
//        $this->assertEquals(7, $historyArr['loan.period_final']->value_to);
//        $this->assertEquals(2, $historyArr['loan.loan_status_id']->value_to);
//        $this->assertEquals(3, $historyArr['loan.payment_method_id']->value_to);
//        $this->assertEquals(2, $historyArr['loan.office_id']->value_to);
//        $this->assertEquals($dbLoan->amount_approved - $debt, $historyArr['loan.amount_rest']->value_to);
//        $this->assertEquals(36, $historyArr['loan.interest_percent']->value_to);
//        $this->assertEquals(240.01, $historyArr['loan.penalty_percent']->value_to);
//        $this->assertEquals("+1 days", $historyArr['loan.installment_modifier']->value_to);
    }
}
