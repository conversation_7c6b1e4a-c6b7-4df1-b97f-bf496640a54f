<?php

namespace Modules\Head\Tests\Feature;

use Modules\Common\Models\Installment;
use Modules\Common\Providers\TestDataProvider;
use Modules\Head\Console\RecalculateStatsCommand;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\Sales\Application\Actions\NewAppAction;
use Modules\Sales\Http\Dto\ClientDto;
use Modules\Sales\Http\Middleware\DtoSerializerNewClientLoan;
use Tests\TestCase;

class RecalculateStatsCommandTest extends TestCase
{
    use DatabaseTransactions;
    private ClientDto $dto;
    private NewAppAction $newAppAction;

    protected function setUp(): void
    {
        parent::setUp();
        $this->dto = TestDataProvider::get(
            'applicationRequest1',
            DtoSerializerNewClientLoan::class,
            'createClientDto'
        );
        $this->newAppAction = app(NewAppAction::class);
    }

    public function testHappyPathClient()
    {
        $client = $this->newAppAction->execute($this->dto)->dbModel()->refresh();
        $this->assertNotNull($client->clientActualStats);
        $stats = $client->clientActualStats;
        $stats->applications_count = 2;
        $stats->save();
        $this->artisan(RecalculateStatsCommand::name(), ['clientId'=>$client->getKey()])->assertSuccessful();
        $this->assertEquals(1, $stats->refresh()->applications_count);
    }

    public function testHappyPathLoan()
    {
        $loan = $this->newAppAction->execute($this->dto)->dbLoan()->refresh();
        $this->assertNotNull($loan->loanActualStats);
        $stats = $loan->loanActualStats;
        $stats->has_payment = 1;
        $stats->save();
        $this->assertCount(1, $loan->installments);
        /** @var Installment $installment */
        $installment = $loan->installments->first();
        $installment->principal = 666;
        $installment->save();

        $this->artisan(RecalculateStatsCommand::name(), ['loanId'=>$loan->getKey()])->assertSuccessful();
        $this->assertEquals(0, $stats->refresh()->has_payment);
        $this->assertEquals(200.00, $installment->refresh()->principal);
    }

}
