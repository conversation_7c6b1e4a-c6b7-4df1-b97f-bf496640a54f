<?php

namespace StikCredit\Calculators;

use StikCredit\Calculators\Config\LoanConfig;
use StikCredit\Calculators\Traits\DatesAwareTrait;
use StikCredit\Calculators\Traits\MathAwareTrait;

class SliderCalculator
{
    use DatesAwareTrait, MathAwareTrait;

    public float $gpr;
    public int $principleAmount = 0;
    public int $installmentAmount = 0;
    public int $interestAmount = 0;
    public int $penaltyAmount = 0;
    public int $discountInstallmentAmount = 0;
    public int $discountInterestAmount = 0;
    public int $discountPenaltyAmount = 0;

    public function __construct(
        private LoanConfig $loanConfig
    )
    {
        $this->loanConfig = LoanConfig::instance();
    }

    public function build(array $loanConfig = []): void
    {
        $this->loanConfig->build($loanConfig);
        if (!$this->loanConfig->isValid()) {
            throw new \Exception('Invalid loan config');
        }

        $this->principleAmount = $this->loanConfig->amount;
        $this->gpr = $this->calculateGpr();

        $airInterest = $this->getAirInterestRate();
        $penaltyInterest = $this->getPenaltyInterestRate();

        $installmentAmount = $this->installmentAmount = $this->getInstallmentAmount();
        $interestAmount = $this->interestAmount = $this->getInterest($this->principleAmount, $airInterest);
        $penaltyAmount = $this->penaltyAmount = $this->getInterest($this->principleAmount, $penaltyInterest);


        if ($this->loanConfig->hasDiscount()) {
            $this->gpr = $this->calculateGpr();
            $airInterest = $this->getAirInterestRate();
            $penaltyInterest = $this->getPenaltyInterestRate();

            $this->installmentAmount = $this->getDiscountInstallmentAmount();
            $this->discountInstallmentAmount = ($installmentAmount - $this->installmentAmount);

            $this->interestAmount = $this->getInterest($this->principleAmount, $airInterest);
            $this->discountInterestAmount = ($interestAmount - $this->interestAmount);

            $this->penaltyAmount = $this->getInterest($this->principleAmount, $penaltyInterest);
            $this->discountPenaltyAmount = ($penaltyAmount - $this->penaltyAmount);
        }
    }

    public static function instance(): SliderCalculator
    {
        return new SliderCalculator(new LoanConfig());
    }
}
