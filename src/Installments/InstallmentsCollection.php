<?php

namespace StikCredit\Calculators\Installments;

use StikCredit\Calculators\Abstract\AbstractLoanCollection;
use StikCredit\Calculators\Fees\FeesCollection;

/**
 * @method DefaultInstallment first()
 * @method DefaultInstallment last()
 * @method DefaultInstallment get(int $key, $default = null)
 * @property-read DefaultInstallment $dueDate;
 */
class InstallmentsCollection extends AbstractLoanCollection
{

    public function build(array $hasCustomPaymentSchedule): InstallmentsCollection
    {
        if (!empty($hasCustomPaymentSchedule)) {
            return $this->buildFromExisting($hasCustomPaymentSchedule);
        }

        $loanConfig = $this->getConfig();
        $numberOfInstallments = $loanConfig->getNumberOfInstallments();
        $outstandingPrinciple = $loanPrinciple = $loanConfig->amount;
        $graceDays = $loanConfig->graceDays;


        $installmentModifier = $loanConfig->installmentModifier;
        $utilisationDate = $loanConfig->utilisationDate;
        $startFromDate = $utilisationDate; // always start from here
        $period = $loanConfig->numberOfInstallments; // loan.period_approved


        $airInterestRate = $this->getAirInterestRate();
        $penaltyInterestRate = $this->getPenaltyInterestRate();

        /// init all installments
        for ($index = 1; $index <= $numberOfInstallments; $index++) {

            $installmentAmount = $this->getInstallmentAmount();
            $interestAmount = $this->getInterest($loanPrinciple, $airInterestRate);

            $penaltyAmount = 0;
            if ($penaltyInterestRate) {
                $penaltyAmount = $this->getInterest($loanPrinciple, $penaltyInterestRate);
                if ($index == $numberOfInstallments) {
                    $penaltyAmount = ($installmentAmount - $loanPrinciple - $interestAmount);
                }
            }

            if (!$penaltyInterestRate) {
                if ($index == $numberOfInstallments) {
                    $penaltyAmount = ($installmentAmount - $loanPrinciple - $interestAmount);
                }
            }

            $passedDays = $this->dateBuilder->dateDiff($loanConfig->utilisationDate, $loanConfig->currentDate);
            if ($graceDays > $passedDays && $loanConfig->isProductType('payday')) {
                $penaltyAmount = $interestAmount = 0;
                $installmentAmount = $loanPrinciple;
            }

            $totalInterest = ($interestAmount + $penaltyAmount);
            if ($index == $numberOfInstallments) {
                $penaltyAmount = (!$penaltyInterestRate) ? 0 : $penaltyAmount;
            }

            $principal = ($installmentAmount - $totalInterest);



            $startFromDate = $this->getInstallmentStartDate(
                $utilisationDate,
                $startFromDate,
                $installmentModifier,
                $period
            );


            /// create installment object
            $installment = new DefaultInstallment(
                $index,
                $startFromDate,
                $interestAmount,
                $penaltyAmount,
                $outstandingPrinciple,
                $principal,
                $installmentAmount
            );
            $installment->build();

            $this->put($index, $installment);

            $outstandingPrinciple = $loanPrinciple = ($loanPrinciple - ($installmentAmount - $totalInterest));
        }

        return $this;
    }

    public function getInstallmentStartDate(
        string $utilisationDate,
        string $oldStartDate,
        string $installmentModifier,
        ?int $period = null // needed only for payday loans, where $installmentModifier = "+1 days"
    ) {
        $date = new \DateTime($oldStartDate);
        if ('+1 month' == $installmentModifier) {
            $date->modify('first day of this month');
        }

        $originalDate = new \DateTime($utilisationDate);
        $originalDay = (int) $originalDate->format('d');

        switch ($installmentModifier) {
            case '+1 days':
                if (empty($period)) {
                    throw new \Exception('Period is not passed for getLoanStartDate()');
                }
                $date->add(new \DateInterval('P' . $period . 'D'));
                break;

            case '+7 days':
                $date->add(new \DateInterval('P7D'));
                break;

            case '+14 days':
                $date->add(new \DateInterval('P14D'));
                break;

            case '+1 month':
                // Add a month
                $date->add(new \DateInterval('P1M'));

                // Attempt to set the day to the same as the original start date
                $year = $date->format('Y');
                $month = $date->format('m');
                $daysInMonth = $date->format('t'); // Total days in the new month

                if ($originalDay > $daysInMonth) {
                    $date->setDate($year, $month, $daysInMonth);
                } else {
                    $date->setDate($year, $month, $originalDay);
                }
                break;

            default:
                throw new \Exception('Wrong installment modifier for getLoanStartDate()');
        }

        return $date->format('Y-m-d');
    }

    public function buildFromExisting(array $hasCustomPaymentSchedule): self
    {
        collect($hasCustomPaymentSchedule)->map(function (array $installment) {
            $defaultInstallment = new DefaultInstallment(
                $installment['seq_num'],
                $installment['due_date'],
                $installment['interest'],
                $installment['penalty'],
                $installment['rest_principal'],
                $installment['principal'],
                $installment['total_amount'],
            );
            $defaultInstallment->build();

            $this->put($installment['seq_num'], $defaultInstallment);
        });

        return $this;
    }

    public function setFirstAndLastInstallment(): self
    {
        $this->each(function (DefaultInstallment $defaultInstallment, $index) {
            $defaultInstallment->isFirst = $index === 1;
            $defaultInstallment->isLast = $index === $this->last()->index;
        });

        return $this;
    }

    public function setPassedDays(): InstallmentsCollection
    {

        $this->each(function (DefaultInstallment $defaultInstallment, int $index) {
            $fromDate = $this->dateBuilder->sqlDate($this->loanConfig->utilisationDate);
            if (!$defaultInstallment->isFirst) {
                $fromDate = $this->dateBuilder->sqlDate($this->prev($index)->dueDate);
            }

            $toDate = $this->dateBuilder->sqlDate();
            if ($defaultInstallment->isDue) {
                $toDate = $this->dateBuilder->sqlDate($defaultInstallment->dueDate);
            }

            $juridicalCaseOpenedAt = $this->sqlDate($this->loanConfig->juridicalCaseOpenedAt);
            if ($this->loanConfig->isJuridical && $toDate > $juridicalCaseOpenedAt) {
                $toDate = $juridicalCaseOpenedAt;
            }

            if ($defaultInstallment->isDue || $defaultInstallment->isCurrent) {
                $defaultInstallment->passedDays = $this->dateBuilder->dateDiff($fromDate, $toDate?->format('Y-m-d'));
            }
        });

        return $this;
    }

    public function setOverdueDaysFromLastPaymentDate(): InstallmentsCollection
    {
        $this->each(function (DefaultInstallment $defaultInstallment) {
            if (!$defaultInstallment->lastPaymentDate) {
                return true; /// if not set last payment date continue
            }

            $currentDate = $this->loanConfig->currentDate;
            $defaultInstallment->overdueDaysFromLastPaymentDate = $this->getDiffDays($defaultInstallment->lastPaymentDate, $currentDate);
        });

        return $this;
    }

    public function setOverdueDays(string $paymentDate = null): InstallmentsCollection
    {
        $this->each(function (DefaultInstallment $defaultInstallment, int $index) use ($paymentDate) {
            $fromDate = $this->dateBuilder->sqlDate($defaultInstallment->dueDate);
            $toDate = $paymentDate ?? $this->dateBuilder->sqlDate();
            if (is_string($toDate)) {
                $toDate = $this->dateBuilder->sqlDate($toDate);
            }

            if ($defaultInstallment->isPaid) {
                $toDate = $this->dateBuilder->sqlDate($defaultInstallment->paidDate);
                $defaultInstallment->overdueDays = $this->dateBuilder->dateDiff($fromDate, $toDate?->format('Y-m-d'));

                if ($fromDate->gt($toDate)) {
                    $defaultInstallment->overdueDays = -$this->dateBuilder->dateDiff($fromDate, $toDate?->format('Y-m-d'));
                }
            }

            $juridicalCaseOpenedAt = $this->sqlDate($this->loanConfig->juridicalCaseOpenedAt);
            if ($this->loanConfig->isJuridical && $toDate > $juridicalCaseOpenedAt) {
                $toDate = $juridicalCaseOpenedAt;
            }

            if ($defaultInstallment->isDue && !$defaultInstallment->isPaid) {
                $defaultInstallment->overdueDays = $this->dateBuilder->dateDiff($fromDate, $toDate?->format('Y-m-d'));
                if ($fromDate->gt($toDate)) {
                    $defaultInstallment->overdueDays = -$defaultInstallment->overdueDays;
                }
            }

        });

        return $this;
    }

    public function setInstallmentDays(): InstallmentsCollection
    {
        $this->each(function (DefaultInstallment $defaultInstallment, $index) {

            if ($this->loanConfig->isProductType('payday')) {
                $defaultInstallment->installmentDays = $this->loanConfig->numberOfInstallments;

                return false;
            }

            $fromDate = $this->dateBuilder->sqlDate($defaultInstallment->dueDate);
            $toDate = $this->dateBuilder->sqlDate($defaultInstallment->dueDate)->modify($this->loanConfig->installmentModifier);
            $defaultInstallment->installmentDays = $this->dateBuilder->dateDiff($fromDate, $toDate);
        });

        return $this;
    }

    public function setDueInstallments(): InstallmentsCollection
    {
        $this->each(function (DefaultInstallment $defaultInstallment) {
            $dueDateTimestamp = $this->dateBuilder->sqlDate($defaultInstallment->dueDate)->getTimestamp();
            $currentDateTimestamp = $this->dateBuilder->sqlDate()->getTimestamp();

            if ($dueDateTimestamp <= $currentDateTimestamp) {
                $defaultInstallment->isDue = true;
            }
        });

        return $this;
    }

    public function setCurrentInstallment(): InstallmentsCollection
    {
        $this->each(function (DefaultInstallment $defaultInstallment) {

            if ($defaultInstallment->isPaid) {
                $defaultInstallment->isCurrent = false;
                return true;
            }

            if (!$defaultInstallment->isDue) {
                $defaultInstallment->isCurrent = true;
                return false;
            }
        });

        return $this;
    }


    public function getTotalAmountWithFees(): int
    {
        $totalInstallmentAmount = $this->sum('installmentAmount');

        /** @var FeesCollection $feesCollection * */
        $feesCollection = $this->diContainer->get(FeesCollection::class);
        $dueFeesAmount = $feesCollection->getDueFees()->sum('amount');

        return array_sum([$totalInstallmentAmount, $dueFeesAmount]);
    }

    public function getAccruedPrincipalAmount(): int
    {
        return $this->sum(function (DefaultInstallment $defaultInstallment) {
            if ($defaultInstallment->isDue()) {
                return $defaultInstallment->principal;
            }
            return 0;
        });
    }

    public function getUnpaidDueAmount(string $key): int
    {
        return $this->sum(function (DefaultInstallment $defaultInstallment) use ($key) {
            if ($defaultInstallment->isDue() && !$defaultInstallment->isPaid) {
                return $defaultInstallment->$key;
            }
            return 0;
        });
    }

    public function getDueAmount(string $key): int
    {
        return $this->sum(function (DefaultInstallment $defaultInstallment) use ($key) {
            if ($defaultInstallment->isDue() && !$defaultInstallment->isPaid()) {
                return $defaultInstallment->$key;
            }
            return 0;
        });
    }
}
