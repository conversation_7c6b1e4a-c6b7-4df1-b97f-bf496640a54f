<?php

namespace StikCredit\Calculators\Installments;

use StikCredit\Calculators\Fees\FeesCollection;

/**
 * @property  int $index
 * @property  string $dueDate
 * @property  int $interest
 * @property  int $penaltyAmount
 * @property  int $outstandingPrincipal
 * @property  int $principal
 * @property  int $installmentAmount
 * @property  bool $isDue
 * @property  bool $isPaid
 * @property  bool $partiallyPaid
 * @property  bool|string $paidDate
 * @property  string $dueLabel
 * @property  int $passedDays
 * @property  int $overdueDays
 * @property  int $installmentDays
 *
 * @method DefaultInstallment first(callable $callback = null, $default = null)
 * @method DefaultInstallment get($key, $default = null)
 * @method DefaultInstallment last(callable $callback = null, $default = null)
 */
class DefaultInstallment extends AbstractInstallment
{
    public function __construct(
        public int         $index,
        public string      $dueDate,
        public int         $interest,
        public int         $penaltyAmount,
        public int         $outstandingPrincipal,
        public int         $principal,
        public int         $installmentAmount,

        public bool        $isDue = false,
        public bool        $isPaid = false,
        public bool        $isCurrent = false,
        public bool        $isFirst = false,
        public bool        $isLast = false,
        public bool        $partiallyPaid = false,

        public bool|string $paidDate = false,
        public string      $dueLabel = '',

        public int         $passedDays = 0,
        public int         $overdueDays = 0,
        public int         $installmentDays = 0,
        public ?string     $lastPaymentDate = null,
        public int         $overdueDaysFromLastPaymentDate = 0,
    )
    {
    }

    public function build(): void
    {
        $this->dueLabel = $this->getDueLabel();
    }

    public function getOverdueDaysFromLastPaymentDate(string $paymentDate): int
    {
        if (!$this->lastPaymentDate || $this->isPaid) {
            return 0;
        }

        $this->overdueDaysFromLastPaymentDate = $this->getDiffDays($this->lastPaymentDate, $paymentDate);

        return $this->overdueDaysFromLastPaymentDate;
    }

    public function getCssLabel()
    {
        $dueLabelCssClass = [
            'paidInstallment' => 'bg-success',
            'incomingInstallment' => 'bg-warning',
            'pastInstallment' => 'bg-info',
            'overdueInstallment' => 'bg-danger',
        ];

        return $dueLabelCssClass[$this->getDueLabel()];
    }

    public function getTotalInstallmentAmountWithFees(): int
    {
        $installmentAmount = array_sum([
            $this->penaltyAmount,
            $this->interest,
            $this->principal,
        ]);

        /** @var FeesCollection $feesCollection * */
        $feesCollection = $this->loanGet(FeesCollection::class);
        $installmentFeesAmount = $feesCollection->getDueFeesToInstallment($this->index)->sum('amount');

        return array_sum([
            $installmentAmount,
            $installmentFeesAmount
        ]);
    }

    public function getTotalInstallmentAmount(): int
    {
        return array_sum([
            $this->penaltyAmount,
            $this->interest,
            $this->principal,
        ]);
    }

    public function isPast(): bool
    {
        if ($this->sqlDate($this->dueDate)->getTimestamp() == $this->sqlDate()->getTimestamp()) {
            return true;
        }
        return false;
    }

    public function isDue(): bool
    {
        return ($this->sqlDate($this->dueDate)->getTimestamp() <= $this->sqlDate()->getTimestamp());
    }
}
