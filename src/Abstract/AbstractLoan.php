<?php

namespace StikCredit\Calculators\Abstract;

use StikCredit\Calculators\DiContainer;
use StikCredit\Calculators\Installments\DefaultInstallment;
use StikCredit\Calculators\Installments\InstallmentsCollection;

/**
 * @property int $index
 */
class AbstractLoan
{
    public function isPartiallyPaid(): bool
    {
        if ($this instanceof DefaultInstallment) {
            return $this->partiallyPaid;
        }

        $installment = $this->getInstallmentCollection()->get($this->index);

        return $installment->partiallyPaid;
    }

    public function setIsPaid(string $paidDate): void
    {
        $installmentCollection = $this->getInstallmentCollection();

        /**
         * @var DefaultInstallment $defaultInstallment
         */
        $defaultInstallment = $installmentCollection->get($this->index);

        $defaultInstallment->isPaid = true;
        $defaultInstallment->paidDate = $paidDate;
    }

    public function getOverdueDays(): int
    {
        /** @phpstan-ignore-next-line * */
        if ($this instanceof DefaultInstallment) {
            return $this->overdueDays;
        }

        $installmentCollection = $this->getInstallmentCollection();

        return $installmentCollection->get($this->index)->overdueDays;
    }

    public function isDue(): bool
    {
        /** @phpstan-ignore-next-line * */
        if ($this instanceof DefaultInstallment) {
            return $this->isDue();
        }

        $installmentCollection = $this->getInstallmentCollection();

        return $installmentCollection->get($this->index)->isDue();
    }

    public function isPaid(): bool
    {
        /** @phpstan-ignore-next-line * */
        if ($this instanceof DefaultInstallment) {
            return $this->isPaid;
        }

        $installmentCollection = $this->getInstallmentCollection();

        return $installmentCollection->get($this->index)->isPaid;
    }

    protected function getInstallmentCollection(): InstallmentsCollection
    {
        return DiContainer::instance()->get(InstallmentsCollection::class);
    }
}
