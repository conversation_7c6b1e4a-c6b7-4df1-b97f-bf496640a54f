<?php

namespace StikCredit\Calculators;

use Illuminate\Support\Collection;
use StikCredit\Calculators\Accrued\AccruedAmountsCollection;
use StikCredit\Calculators\Config\LoanConfig;
use StikCredit\Calculators\Fees\DefaultFee;
use StikCredit\Calculators\Fees\FeesCollection;
use StikCredit\Calculators\FreshInstallment\FreshInstallmentCollection;
use StikCredit\Calculators\Installments\InstallmentsCollection;
use StikCredit\Calculators\LateAmount\LateAmountCollection;
use StikCredit\Calculators\LoanState\LoanStateCollection;
use StikCredit\Calculators\Outstanding\OutstandingAmountCollection;
use StikCredit\Calculators\Overdue\OverdueAmountCollection;
use StikCredit\Calculators\Paid\PaidAmountCollection;
use StikCredit\Calculators\PaymentDistribution\PaymentDistributionCollection;
use StikCredit\Calculators\Payments\ReceivedPaymentsCollection;
use StikCredit\Calculators\Traits\MathAwareTrait;

/**
 * @property  LoanConfig $loanConfig
 * @property  LoanCarton $loanCarton
 * @property  FeesCollection $feesCollection
 * @property  InstallmentsCollection $installmentsCollection
 * @property  AccruedAmountsCollection $accruedAmountsCollection
 * @property  OutstandingAmountCollection $outstandingAmountCollection
 * @property  ReceivedPaymentsCollection $receivedPaymentsCollection
 * @property  PaymentDistributionCollection $paymentDistributionCollection
 * @property  PaidAmountCollection $paidAmountCollection
 * @property  OverdueAmountCollection $overdueAmountCollection
 * @property  LateAmountCollection $lateAmountCollection
 * @property  FreshInstallmentCollection $freshInstallmentCollection
 */
class LoanCalculator
{
    use MathAwareTrait;

    public float $gpr;

    public int $divider;

    protected DiContainer $diContainer;

    protected LoanConfig $loanConfig;

    public function __construct(
        private readonly LoanCarton                    $loanCarton,
        private readonly FeesCollection                $feesCollection,
        private readonly InstallmentsCollection        $installmentsCollection,
        private readonly AccruedAmountsCollection      $accruedAmountsCollection,
        private readonly OutstandingAmountCollection   $outstandingAmountCollection,
        private readonly ReceivedPaymentsCollection    $receivedPaymentsCollection,
        private readonly PaymentDistributionCollection $paymentDistributionCollection,
        private readonly PaidAmountCollection          $paidAmountCollection,
        private readonly OverdueAmountCollection       $overdueAmountCollection,
        private readonly LateAmountCollection          $lateAmountCollection,
        private readonly FreshInstallmentCollection    $freshInstallmentCollection,
    )
    {
        $this->loanConfig = LoanConfig::instance();
        $this->diContainer = DiContainer::instance();
    }

    public function build(array $loanConfig = []): LoanCalculator
    {
        $receivedPayments = $loanConfig['receivedPayments'] ?? [];
        $fees = $loanConfig['fees'] ?? [];
        $hasCustomPaymentSchedule = $loanConfig['hasCustomPaymentSchedule'] ?? [];

        unset(
            $loanConfig['receivedPayments'],
            $loanConfig['fees'],
            $loanConfig['hasCustomPaymentSchedule']
        );

        $this->loanConfig->build($loanConfig);
        if (!$this->loanConfig->isValid()) {
            throw new \Exception('Invalid loan config');
        }
        $this->gpr = $this->calculateGpr();
        $this->divider = $this->loanConfig->divider;
        if (!empty($hasCustomPaymentSchedule)) {
            $this->loanConfig->hasCustomPaymentSchedule = true;
        }

        $this
            ->buildReceivedPaymentsCollection($receivedPayments)
            ->buildFeesCollection($fees)
            ->buildInstallmentsCollection($hasCustomPaymentSchedule)
            ->buildAccruedAmountsCollection()
            ->buildOutstandingAmountCollection($hasCustomPaymentSchedule)
            ->buildOverdueAmountCollection()
            ->buildLateAmountCollection()
            ->buildPaidAmountCollection()
            ->buildPaymentDistributionCollection($hasCustomPaymentSchedule)
            ->buildFreshInstallmentCollection();

        return $this;
    }

    public function isRepaid(): bool
    {
        $outstandingAmount = $this->outstandingAmountCollection()->getTotalOutstandingAmount();

        /// todo if amount smaller than 10.00 lv written_of and return true ?
        if ($outstandingAmount <= 0) {
            return true;
        }

        return false;
    }

    public function loanCarton(): LoanCarton
    {
        $this->loanCarton->build();

        return $this->loanCarton;
    }

    public function loanConfig(): LoanConfig
    {
        return $this->loanConfig;
    }

    public function loanStateCollection(): LoanStateCollection
    {
        $receivedPaymentsCollection = $this->diContainer->get(ReceivedPaymentsCollection::class);

        return $this->loanStateCollection->build($this->originalConfigArray, $receivedPaymentsCollection);
    }

    public function receivedPaymentsCollection(): ReceivedPaymentsCollection
    {
        return $this->receivedPaymentsCollection;
    }

    public function overdueAmountCollection(): OverdueAmountCollection
    {
        return $this->overdueAmountCollection;
    }

    public function paidAmountCollection(): PaidAmountCollection
    {
        return $this->paidAmountCollection;
    }

    public function paymentDistributionCollection(): PaymentDistributionCollection
    {
        return $this->paymentDistributionCollection;
    }

    public function outstandingAmountCollection(): OutstandingAmountCollection
    {
        return $this->outstandingAmountCollection;
    }

    public function accruedAmountsCollection(): AccruedAmountsCollection
    {
        return $this->accruedAmountsCollection;
    }

    public function installmentsCollection(): InstallmentsCollection
    {
        return $this->installmentsCollection;
    }

    public function lateAmountCollection(): LateAmountCollection
    {
        return $this->lateAmountCollection;
    }

    public function feesCollection(int $index = null): Collection|DefaultFee
    {
        if ($index) {
            return $this->feesCollection->getDueFeesToInstallment($index);
        }
        return $this->feesCollection->sortBy('dueDate');
    }

    public function freshInstallmentCollection(): FreshInstallmentCollection
    {
        return $this->freshInstallmentCollection;
    }


    ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /// Build functions
    ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

    public function buildLoanStateCollection(): LoanCalculator
    {
        $this->diContainer->bind(LoanStateCollection::class, $this->loanStateCollection->build());

        return $this;
    }

    protected function buildFreshInstallmentCollection(): LoanCalculator
    {
        $this->freshInstallmentCollection->build();

        $this->diContainer->bind(FreshInstallmentCollection::class, $this->freshInstallmentCollection);

        return $this;
    }

    protected function buildLateAmountCollection(): LoanCalculator
    {
        $this->lateAmountCollection->build();

        $this->diContainer->bind(LateAmountCollection::class, $this->lateAmountCollection);

        return $this;
    }

    protected function buildOverdueAmountCollection(): LoanCalculator
    {
        $this->overdueAmountCollection
            ->build()
            ->setOverdueAmount();

        $this->diContainer->bind(OverdueAmountCollection::class, $this->overdueAmountCollection);

        return $this;
    }

    protected function buildPaidAmountCollection(): LoanCalculator
    {
        $this->paidAmountCollection->build();

        $this->diContainer->bind(PaidAmountCollection::class, $this->paidAmountCollection);

        return $this;
    }

    protected function buildPaymentDistributionCollection(array $hasCustomPaymentSchedule): LoanCalculator
    {
        $paymentDistributionCollection = $this->paymentDistributionCollection->build();

        $this->diContainer->bind(PaymentDistributionCollection::class, $paymentDistributionCollection);

        //// run payment
        $paymentDistributionCollection->execute($hasCustomPaymentSchedule);

        return $this;
    }

    protected function buildReceivedPaymentsCollection(array $receivedPayments = []): LoanCalculator
    {
        $this->receivedPaymentsCollection->build($receivedPayments);

        $this->diContainer->bind(ReceivedPaymentsCollection::class, $this->receivedPaymentsCollection);

        return $this;
    }

    protected function buildFeesCollection(array $loanFees = []): LoanCalculator
    {
        $this->feesCollection->build($loanFees);

        $this->diContainer->bind(FeesCollection::class, $this->feesCollection);

        return $this;
    }

    protected function buildOutstandingAmountCollection(array $hasCustomPaymentSchedule): LoanCalculator
    {
        $this->outstandingAmountCollection->build($hasCustomPaymentSchedule);

        $this->diContainer->bind(OutstandingAmountCollection::class, $this->outstandingAmountCollection);

        return $this;
    }

    protected function buildAccruedAmountsCollection(): LoanCalculator
    {
        $this->accruedAmountsCollection->build();

        $this->diContainer->bind(AccruedAmountsCollection::class, $this->accruedAmountsCollection);

        return $this;
    }

    protected function buildInstallmentsCollection(array $hasCustomPaymentSchedule): LoanCalculator
    {
        $installmentsCollection = $this->installmentsCollection->build($hasCustomPaymentSchedule);

        $installmentsCollection
            ->setFirstAndLastInstallment()
            ->setDueInstallments()
            ->setCurrentInstallment()
            ->setInstallmentDays()
            ->setPassedDays()
            ->setOverdueDays();

        $this->diContainer->bind(InstallmentsCollection::class, $this->installmentsCollection);

        return $this;
    }

    public function getDbFees(): array
    {
        return $this->feesCollection->dbFees->toArray();
    }

    public static function instance(): LoanCalculator
    {
        /// before get new instance destroy singleton
        LoanConfig::instance()->destroy();
        DiContainer::instance()->destroy();

        return new LoanCalculator(
            new LoanCarton(),
            new FeesCollection(),
            new InstallmentsCollection(),
            new AccruedAmountsCollection(),
            new OutstandingAmountCollection(),
            new ReceivedPaymentsCollection(),
            new PaymentDistributionCollection(),
            new PaidAmountCollection(),
            new OverdueAmountCollection(),
            new LateAmountCollection(),
            new FreshInstallmentCollection(),
            new LoanStateCollection()
        );
    }
}
