<?php

namespace StikCredit\Calculators;

use StikCredit\Calculators\Accrued\AccruedAmountsCollection;
use StikCredit\Calculators\Config\LoanConfig;
use StikCredit\Calculators\Fees\FeesCollection;
use StikCredit\Calculators\Installments\DefaultInstallment;
use StikCredit\Calculators\Installments\InstallmentsCollection;
use StikCredit\Calculators\Outstanding\OutstandingAmountCollection;
use StikCredit\Calculators\Overdue\OverdueAmountCollection;
use StikCredit\Calculators\Paid\PaidAmountCollection;
use StikCredit\Calculators\Traits\DatesAwareTrait;
use StikCredit\Calculators\Traits\MathAwareTrait;

/**
 * @property  int $principle
 * @property  int $duePrincipal
 * @property  int $accruedPrincipal
 * @property  int $dueUnpaidPrincipal
 * @property  int $duePaidPrincipal
 * @property  int $paidPrinciple
 * @property  int $outstandingPrinciple
 * @property  int $overduePrinciple
 *
 * @property  int $interest
 * @property  int $dueInterest
 * @property  int $accruedInterest
 * @property  int $paidInterest
 * @property  int $outstandingInterest
 * @property  int $dueUnpaidInterest
 * @property  int $duePaidInterest
 * @property  int $overdueInterest
 *
 * /// penalty amounts
 * @property  int $penalty
 * @property  int $duePenalty
 * @property  int $accruedPenalty
 * @property  int $paidPenalty
 * @property  int $outstandingPenalty
 * @property  int $dueUnpaidPenalty
 * @property  int $duePaidPenalty
 * @property  int $overduePenalty
 *
 * /// fees amounts
 * @property  int $totalAccruedFeesAmount
 * @property  int $totalOutstandingFeesAmount
 * @property  int $totalPaidFeesAmount
 * @property  int $totalOverdueFeesAmount
 *
 * /// late amounts
 * @property  int $accruedLatePenalty
 * @property  int $accruedLateInterest
 * @property  int $totalAccruedLateAmount
 *
 * @property  int $paidLateInterest
 * @property  int $paidLatePenalty
 * @property  int $totalPaidLateAmount
 * @property  int $outstandingLateInterest
 * @property  int $outstandingLatePenalty
 * @property  int $totalOutstandingLateAmount
 *
 * /// early repayment
 * @property  int $regularRepaymentAmount
 * @property  int $earlyRepaymentAmount
 *
 * /// current overdue amount
 * @property  int $currentOverdueAmount
 *
 * /// overdue days
 * @property  int $maxOverdueDays
 * @property  int $totalOverdueDays
 * @property  int $currentOverdueDays
 *
 * /// installments
 * @property  int $monthlyInstallmentAmount
 * @property  int $paidInstallments
 * @property  int $overdueInstallments
 *
 * /// totals
 * @property  int $totalPaidAmount
 * @property  int $totalDueAmount
 * @property  int $totalDueAmountWithFees
 * @property  int $totalDueUnpaidAmount
 * @property  int $totalDueUnpaidWithFeesAmount
 * @property  int $totalDuePaidAmount
 * @property  int $totalDueOutstandingAmount
 * @property  int $totalOutstandingPaymentScheduleAmount
 * @property int $totalRemainsAmount
 */
class LoanCarton
{
    use DatesAwareTrait, MathAwareTrait;

    protected InstallmentsCollection $installmentsCollection;
    protected AccruedAmountsCollection $accruedAmountsCollection;

    protected OutstandingAmountCollection $outstandingAmountCollection;

    protected PaidAmountCollection $paidAmountCollection;

    protected OverdueAmountCollection $overdueAmountCollection;

    protected FeesCollection $feesCollection;

    public function __construct(
        public float $gpr = 0.0,

        /// default amounts
        public int   $principle = 0,
        public int   $duePrincipal = 0,
        public int   $accruedPrincipal = 0,
        public int   $dueUnpaidPrincipal = 0,
        public int   $duePaidPrincipal = 0,
        public int   $paidPrinciple = 0,
        public int   $outstandingPrinciple = 0,
        public int   $overduePrinciple = 0,

        /// interest amounts
        public int   $interest = 0,
        public int   $dueInterest = 0,
        public int   $accruedInterest = 0,
        public int   $paidInterest = 0,
        public int   $outstandingInterest = 0,
        public int   $dueUnpaidInterest = 0,
        public int   $duePaidInterest = 0,
        public int   $overdueInterest = 0,

        /// penalty amounts
        public int   $penalty = 0,
        public int   $duePenalty = 0,
        public int   $accruedPenalty = 0,
        public int   $paidPenalty = 0,
        public int   $outstandingPenalty = 0,
        public int   $dueUnpaidPenalty = 0,
        public int   $duePaidPenalty = 0,
        public int   $overduePenalty = 0,

        /// fees amounts
        public int   $totalAccruedFeesAmount = 0,
        public int   $totalOutstandingFeesAmount = 0,
        public int   $totalPaidFeesAmount = 0,
        public int   $totalOverdueFeesAmount = 0,

        /// late amounts
        public int   $accruedLatePenalty = 0,
        public int   $totalAccruedLatePenalty = 0,
        public int   $accruedLateInterest = 0,
        public int   $totalAccruedLateInterest = 0,
        public int   $totalAccruedLateAmount = 0,

        public int   $paidLateInterest = 0,
        public int   $paidLatePenalty = 0,
        public int   $totalPaidLateAmount = 0,

        public int   $outstandingLateInterest = 0,
        public int   $outstandingLatePenalty = 0,
        public int   $totalOutstandingLateAmount = 0,

        /// early repayment
        public int   $regularRepaymentAmount = 0,
        public int   $earlyRepaymentAmount = 0,

        /// current overdue amount
        public int   $currentOverdueAmount = 0,

        /// overdue days
        public int   $maxOverdueDays = 0,
        public int   $totalOverdueDays = 0,
        public int   $currentOverdueDays = 0,

        /// installments
        public int   $monthlyInstallmentAmount = 0,
        public int   $paidInstallments = 0,
        public int   $overdueInstallments = 0,

        /// totals
        public int   $totalPaidAmount = 0,
        public int   $totalDueAmount = 0,
        public int   $totalDueAmountWithFees = 0,
        public int   $totalDueUnpaidAmount = 0,
        public int   $totalDueUnpaidWithFeesAmount = 0,
        public int   $totalDuePaidAmount = 0,
        public int   $totalDueOutstandingAmount = 0,
        public int   $totalOutstandingPaymentScheduleAmount = 0,
        public int   $totalRemainsAmount = 0,
        public int   $totalOutstandingAmount = 0,
    )
    {
    }

    public function build(): LoanCarton
    {
        if (!LoanConfig::instance()->isValid()) {
            return $this;
        }

        /// get collections
        $this->installmentsCollection = $this->loanGet(InstallmentsCollection::class);
        $this->accruedAmountsCollection = $this->loanGet(AccruedAmountsCollection::class);
        $this->outstandingAmountCollection = $this->loanGet(OutstandingAmountCollection::class);
        $this->paidAmountCollection = $this->loanGet(PaidAmountCollection::class);
        $this->overdueAmountCollection = $this->loanGet(OverdueAmountCollection::class);
        $this->feesCollection = $this->loanGet(FeesCollection::class);


        $this->gpr = $this->calculateGpr();
        $this
            ->setDefaultAmounts()
            ->setDueAmounts()
            ->setAccruedAmounts()
            ->setPaidAmounts()
            ->setOutstandingAmounts()
            ->setRepaymentAmounts()
            ->setLateAmounts()
            ->setOverdueAmounts()
            ->setFeesAmounts()
            /// other
            ->setOverdueDays()
            ->setPaidInstallments()
            ->setOverdueInstallments()
            ->setTotalsAmount();


        return $this;
    }

    protected function setFeesAmounts(): self
    {
        $this->totalAccruedFeesAmount = $this->feesCollection->getDueFees()->sum('amount');
        $this->totalOutstandingFeesAmount = $this->feesCollection->getDueFees()->sum('outstandingAmount');
        $this->totalPaidFeesAmount = $this->feesCollection->getDueFees()->sum('paidAmount');

        return $this;
    }

    protected function setTotalsAmount(): self
    {
        $this->totalDueUnpaidAmount = array_sum([
            $this->dueUnpaidPrincipal,
            $this->dueUnpaidInterest,
            $this->dueUnpaidPenalty,
            $this->outstandingAmountCollection->getTotalOutstandingLateAmount()
        ]);

        $this->totalDueUnpaidWithFeesAmount = array_sum([
            $this->totalDueUnpaidAmount,
            $this->feesCollection->getDueFees()->sum('amount')
        ]);

        $this->totalDuePaidAmount = array_sum([
            $this->duePaidPrincipal,
            $this->duePaidPenalty,
            $this->duePaidInterest,
            $this->paidLatePenalty,
            $this->paidLateInterest,
            $this->feesCollection->getDueFees()->sum('paidAmount')
        ]);

        $this->totalDueOutstandingAmount = array_sum([
            $this->currentOverdueAmount,
            $this->outstandingAmountCollection->getTotalOutstandingLateAmount()
        ]);

        $this->currentOverdueAmount = array_sum([
            $this->currentOverdueAmount,
            $this->totalOutstandingLateAmount,
        ]);

        $this->totalRemainsAmount = array_sum([
            $this->principle,
            $this->interest,
            $this->penalty,
            $this->totalAccruedLateInterest,
            $this->totalAccruedLatePenalty,
            $this->feesCollection->getDueFees()->sum('amount'),
        ]);

        $this->totalOutstandingAmount = array_sum([
            $this->outstandingPrinciple,
            $this->outstandingInterest,
            $this->outstandingPenalty,
            $this->outstandingLateInterest,
            $this->outstandingLatePenalty,
            $this->totalOutstandingFeesAmount,
        ]);

        return $this;
    }

    protected function setOverdueAmounts(): self
    {
        $this->currentOverdueAmount = $this->overdueAmountCollection->getCurrentOverdueAmount();
        $this->overduePrinciple = $this->overdueAmountCollection->getOverdueAmountByOutstandingKey('outstandingPrincipleAmount');
        $this->overdueInterest = $this->overdueAmountCollection->getOverdueAmountByOutstandingKey('outstandingInterestAmount');
        $this->overduePenalty = $this->overdueAmountCollection->getOverdueAmountByOutstandingKey('outstandingPenaltyAmount');

        return $this;
    }

    protected function setLateAmounts(): self
    {
        $this->accruedLateInterest = $this->accruedAmountsCollection->getAccruedInterestLateAmount();
        $this->accruedLatePenalty = $this->accruedAmountsCollection->getAccruedPenaltyLateAmount();
        $this->totalAccruedLateAmount = (int)array_sum([$this->accruedLateInterest, $this->accruedLatePenalty]);

        $this->paidLatePenalty = $this->paidAmountCollection->getTotalPaidLatePenaltyAmount();
        $this->paidLateInterest = $this->paidAmountCollection->getTotalPaidLateInterestAmount();
        $this->totalPaidLateAmount = (int)array_sum([$this->paidLatePenalty, $this->paidLateInterest]);

//        $this->outstandingLateInterest = $this->outstandingAmountCollection->getOutstandingLateInterest();
//        $this->outstandingLatePenalty = $this->outstandingAmountCollection->getOutstandingLatePenalty();
//        $this->totalOutstandingLateAmount = $this->outstandingAmountCollection->getTotalOutstandingLateAmount();

        $this->outstandingLateInterest = $this->accruedLateInterest - $this->paidLateInterest;
        $this->outstandingLatePenalty = $this->accruedLatePenalty - $this->paidLatePenalty;
        $this->totalOutstandingLateAmount = array_sum([$this->outstandingLateInterest, $this->outstandingLatePenalty]);


//        $this->totalAccruedLateInterest = array_sum([
//            $this->outstandingLateInterest,
//            $this->paidLateInterest,
//        ]);
//
//        $this->totalAccruedLatePenalty = array_sum([
//            $this->outstandingLatePenalty,
//            $this->paidLatePenalty,
//        ]);

        $this->totalAccruedLateInterest = $this->accruedLateInterest;
        $this->totalAccruedLatePenalty = $this->accruedLatePenalty;

        return $this;
    }

    protected function setRepaymentAmounts(): self
    {
//        $this->regularRepaymentAmount = array_sum([
//            $this->installmentsCollection->sum('installmentAmount'),
//            $this->feesCollection->getDueFees()->sum('amount')
//        ]);
        $this->regularRepaymentAmount = $this->outstandingAmountCollection->getTotalOutstandingAmount();

        $outstandingAmountCollection = clone $this->outstandingAmountCollection;
        $outstandingAmountCollection->buildFromAccrued();

        $this->earlyRepaymentAmount = $outstandingAmountCollection->getTotalOutstandingAmount();
        /// when loan have extension and saved payment schedule in the future
        /// in this case (earlyRepaymentAmount) will be larger than regularRepaymentAmount
        if ($this->earlyRepaymentAmount > $this->regularRepaymentAmount) {
            $this->regularRepaymentAmount = $this->earlyRepaymentAmount;
        }

        unset($outstandingAmountCollection);

        return $this;
    }

    protected function setOutstandingAmounts(): self
    {
        /// set amounts
        $this->outstandingPrinciple = $this->outstandingAmountCollection->sum('outstandingPrincipleAmount');
        $this->outstandingInterest = $this->outstandingAmountCollection->sum('outstandingInterestAmount');
        $this->outstandingPenalty = $this->outstandingAmountCollection->sum('outstandingPenaltyAmount');

        $this->totalOutstandingPaymentScheduleAmount = (int)array_sum([
            $this->outstandingPrinciple,
            $this->outstandingInterest,
            $this->outstandingPenalty,
        ]);

        return $this;
    }

    protected function setPaidAmounts(): self
    {
        $this->paidPrinciple = $this->paidAmountCollection->sum('paidPrincipleAmount');
        $this->paidInterest = $this->paidAmountCollection->sum('paidInterestAmount');
        $this->paidPenalty = $this->paidAmountCollection->sum('paidPenaltyAmount');

        $this->duePaidPrincipal = $this->paidAmountCollection->getDuePaidAmountByKey('paidPrincipleAmount');
        $this->duePaidInterest = $this->paidAmountCollection->getDuePaidAmountByKey('paidInterestAmount');
        $this->duePaidPenalty = $this->paidAmountCollection->getDuePaidAmountByKey('paidPenaltyAmount');

        /// totals
        $this->totalPaidAmount = $this->paidAmountCollection->getTotalPaidAmount();

        return $this;
    }

    protected function setAccruedAmounts(): self
    {
        $this->accruedPrincipal = $this->installmentsCollection->getAccruedPrincipalAmount();
        $this->accruedInterest = $this->accruedAmountsCollection->sum('accruedInterestAmount');
        $this->accruedPenalty = $this->accruedAmountsCollection->sum('accruedPenaltyAmount');

        return $this;
    }

    protected function setDefaultAmounts(): self
    {
        /** @phpstan-ignore-next-line * */
        $this->monthlyInstallmentAmount = $this->installmentsCollection->first()->installmentAmount;
        $this->principle = $this->getConfig()->amount;
        $this->interest = $this->installmentsCollection->sum('interest');
        $this->penalty = $this->installmentsCollection->sum('penaltyAmount');

        return $this;
    }

    protected function setDueAmounts(): self
    {
        $this->dueUnpaidPrincipal = $this->installmentsCollection->getUnpaidDueAmount('principal');
        $this->dueUnpaidInterest = $this->accruedAmountsCollection->getDueUnpaidAccruedAmount('accruedInterestAmount');
        $this->dueUnpaidPenalty = $this->accruedAmountsCollection->getDueUnpaidAccruedAmount('accruedPenaltyAmount');

        $this->duePrincipal = $this->installmentsCollection->getDueAmount('principal');
        $this->dueInterest = $this->installmentsCollection->getDueAmount('interest');
        $this->duePenalty = $this->installmentsCollection->getDueAmount('penaltyAmount');
        $this->totalDueAmount = (int)array_sum([
            $this->duePrincipal,
            $this->dueInterest,
            $this->duePenalty
        ]);
        $this->totalDueAmountWithFees = (int)array_sum([
            $this->duePrincipal,
            $this->dueInterest,
            $this->duePenalty,
            $this->feesCollection->getDueFees()->sum('outstandingAmount')
        ]);

        return $this;
    }

    protected function setOverdueInstallments(): self
    {
        $this->overdueInstallments = $this->installmentsCollection->sum(function (DefaultInstallment $defaultInstallment) {
            if ($defaultInstallment->isDue() && !$defaultInstallment->isPaid) {
                return 1;
            }
            return null;
        });

        return $this;
    }

    protected function setPaidInstallments(): self
    {
        $this->paidInstallments = $this->installmentsCollection->sum(function (DefaultInstallment $defaultInstallment) {
            if ($defaultInstallment->isPaid) {
                return 1;
            }
            return null;
        });

        return $this;
    }

    protected function setOverdueDays(): self
    {
        $this->maxOverdueDays = $this->installmentsCollection->max('overdueDays');
        $this->installmentsCollection->each(function (DefaultInstallment $defaultInstallment) {
            if ($defaultInstallment->isDue() && !$defaultInstallment->isPaid) {
                $this->currentOverdueDays = $defaultInstallment->overdueDays;
                return false;
            }
        });
        $this->totalOverdueDays = $this->installmentsCollection->sum(function ($row) {
            if ($row->overdueDays > 0) {
                return $row->overdueDays;
            }
            return 0;
        });

        return $this;
    }
}
