<?php

namespace StikCredit\Calculators\Outstanding;

use Illuminate\Support\Collection;
use StikCredit\Calculators\Abstract\AbstractLoan;
use StikCredit\Calculators\Fees\DefaultFee;
use StikCredit\Calculators\PaymentDistribution\DefaultPaymentDistribution;
use StikCredit\Calculators\Payments\DefaultPayment;
use StikCredit\Calculators\Traits\DatesAwareTrait;
use StikCredit\Calculators\Traits\MathAwareTrait;

/**
 * @property DefaultFee[]|Collection $outstandingGroupedFees;
 */
class AbstractOutstandingAmounts extends AbstractLoan
{
    use DatesAwareTrait, MathAwareTrait;

    public function payLoanFees(DefaultPayment $payment, &$amount, &$paymentDistributionCollection): void
    {
        /** @var DefaultFee $loanFee * */
        foreach ($this->outstandingGroupedFees as $loanFee) {

            if ($loanFee->dueDate > $payment->createdAt) {
                continue;
            }

            if ($loanFee->outstandingAmount == 0) {
                continue;
            }

            if ($amount <= 0) {
                break;
            }

            $distributionAmount = $loanFee->outstandingAmount;
            if ($distributionAmount >= $amount) {
                $distributionAmount = $amount;
            }

            $loanFee->outstandingAmount = ($loanFee->outstandingAmount - $distributionAmount);
            if ($loanFee->paidAmount > 0) {
                $loanFee->paidAmount = array_sum([$loanFee->paidAmount, $distributionAmount]);
            } else {
                $loanFee->paidAmount = $distributionAmount;
            }

            /// create payment distribution object
            $identifier = "[{$this->index}][payLoanFees] " . $loanFee->title;
            $paymentDistribution = new DefaultPaymentDistribution(
                $this->index,
                $payment->paymentId,
                $identifier,
                'payLoanFees',
                $payment->createdAt,
                $payment->createdAt,
                $distributionAmount,
                $amount,
                ($amount - $distributionAmount),
                $loanFee->title,
                $payment->hexColor,
                $loanFee->feeId
            );
            $amount = ($amount - $distributionAmount);

            if ($loanFee->outstandingAmount === 0) {
                $loanFee->isPaid = true;
                $loanFee->paidDate = $payment->createdAt;
            }

            $paymentDistributionCollection->push($paymentDistribution);
        }
    }
}
