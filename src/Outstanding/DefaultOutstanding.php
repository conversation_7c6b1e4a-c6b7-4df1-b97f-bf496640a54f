<?php

namespace StikCredit\Calculators\Outstanding;

use Illuminate\Support\Collection;
use StikCredit\Calculators\Config\LoanConfig;
use StikCredit\Calculators\Installments\DefaultInstallment;

/**
 * @property-read DefaultInstallment $paidDate;
 * @property-read DefaultInstallment $overdueDays;
 */
class DefaultOutstanding extends AbstractOutstandingAmounts
{
    public Collection $outstandingGroupedFees;
    public Collection $accruedLateAmounts;

    public function __construct(
        public int $index,
        public int $outstandingPrincipleAmount,
        public int $outstandingInterestAmount,
        public int $outstandingPenaltyAmount,

        ///  late amounts
        public int $outstandingLateInterestAmount = 0,
        public int $outstandingLatePenaltyAmount = 0,
    )
    {
        $this->accruedLateAmounts = collect([]);
    }

    public function getPaymentScheduleOutstanding(): int
    {
        return array_sum([
            $this->outstandingPrincipleAmount,
            $this->outstandingInterestAmount,
            $this->outstandingPenaltyAmount
        ]);
    }

    public function getOutstandingAmount(): int
    {
        $outstandingAmount = array_sum([
            $this->outstandingGroupedFees->sum('outstandingAmount'),
            $this->outstandingPrincipleAmount,
            $this->outstandingInterestAmount,
            $this->outstandingPenaltyAmount
        ]);

        return intval($outstandingAmount);
    }

    public function getTotalOutstandingAmount(): int
    {
        $totalOutstandingAmount = array_sum([
            $this->outstandingGroupedFees->sum('outstandingAmount'),
            $this->outstandingPrincipleAmount,
            $this->outstandingInterestAmount,
            $this->outstandingPenaltyAmount,
            $this->getOutstandingLateInterestAmount(),
            $this->getOutstandingLatePenaltyAmount(),
        ]);

        return intval($totalOutstandingAmount);
    }

    public function calculateLateAmountToDate(string $paymentDate): void
    {
        $hasCustomPaymentSchedule = LoanConfig::instance()->hasCustomPaymentSchedule;

        $installment = $this->getInstallment();
        $overdueDays = $installment->getOverdueDaysToDate($paymentDate);
        if ($installment->lastPaymentDate) {
            $overdueDays = $installment->getOverdueDaysFromLastPaymentDate($paymentDate);
        }

        if ($overdueDays > 0) {
            $outstandingInstallmentAmount = $this->getPaymentScheduleOutstanding();
            $outstandingLatePenaltyAmount = $this->calcAccruedLatePenaltyAmount($outstandingInstallmentAmount, $overdueDays);

            if ($hasCustomPaymentSchedule && $outstandingLatePenaltyAmount > $this->outstandingLatePenaltyAmount) {
                $this->outstandingLatePenaltyAmount = $outstandingLatePenaltyAmount;
            }

            if (!$hasCustomPaymentSchedule && $this->outstandingLatePenaltyAmount !== 0) {
                $this->outstandingLatePenaltyAmount = array_sum([
                    $this->outstandingLatePenaltyAmount,
                    $outstandingLatePenaltyAmount
                ]);
            }

            if (!$hasCustomPaymentSchedule && $this->outstandingLatePenaltyAmount === 0) {
                $this->outstandingLatePenaltyAmount = $outstandingLatePenaltyAmount;
            }

            ////////////////////////////////////////////////////////////////////////////////////////////////////////////
            $outstandingLateInterestAmount = $this->calcAccruedLateAmount($outstandingInstallmentAmount, $overdueDays);
            if ($hasCustomPaymentSchedule && $outstandingLateInterestAmount > $this->outstandingLateInterestAmount) {
                $this->outstandingLateInterestAmount = $outstandingLateInterestAmount;
            }

            if (!$hasCustomPaymentSchedule && $this->outstandingLateInterestAmount !== 0) {
                $this->outstandingLateInterestAmount = array_sum([
                    $this->outstandingLateInterestAmount,
                    $outstandingLateInterestAmount
                ]);
            }

            if (!$hasCustomPaymentSchedule && $this->outstandingLateInterestAmount === 0) {
                $this->outstandingLateInterestAmount = $outstandingLateInterestAmount;
            }


            $this->accruedLateAmounts->push([
                'outstandingLateInterestAmount' => $outstandingLateInterestAmount,
                'outstandingLatePenaltyAmount' => $outstandingLatePenaltyAmount,
            ]);
        }
    }

    public function getAction(string $distributionIdentifier): string
    {
        $actions = [
            'outstandingLateInterestAmount' => 'getOutstandingInterestAmount',
            'outstandingLatePenaltyAmount' => 'getOutstandingPenaltyAndPrincipalAmount',
        ];
        return $actions[$distributionIdentifier];
    }

    public function getOutstandingPenaltyAndPrincipalAmount(): int
    {
        return array_sum([
            $this->outstandingPrincipleAmount,
            $this->outstandingPenaltyAmount,
        ]);
    }

    public function getOutstandingInterestAmount(): int
    {
        return $this->outstandingInterestAmount;
    }

    public function getTotalOutstandingLateAmount(): int
    {
        $sum = array_sum([
            $this->getOutstandingLateInterestAmount(),
            $this->getOutstandingLatePenaltyAmount(),
        ]);
        return intval($sum);
    }

    public function getOutstandingLatePenaltyAmount(): int
    {
        $installment = $this->getInstallment();
        $outstandingLatePenaltyAmount = $this->calcAccruedLateAmount($this->getPaymentScheduleOutstanding(), $installment->getOverdueDays());
        if ($this->getConfig()->hasCustomPaymentSchedule) {
            if ($outstandingLatePenaltyAmount > $this->outstandingLatePenaltyAmount) {
                return $outstandingLatePenaltyAmount;
//                return array_sum([
//                    $outstandingLatePenaltyAmount,
//                    $this->outstandingLatePenaltyAmount
//                ]);
            }
            return $this->outstandingLatePenaltyAmount;
        }

        $this->outstandingLatePenaltyAmount = $this->calcAccruedLatePenaltyAmount($this->getPaymentScheduleOutstanding(), $installment->getOverdueDays());

        return $this->outstandingLatePenaltyAmount;
    }

    public function getOutstandingLateInterestAmount(): int
    {
        $installment = $this->getInstallment();

        $outstandingLateInterestAmount = $this->calcAccruedLateAmount($this->getPaymentScheduleOutstanding(), $installment->getOverdueDays());
        if ($this->getConfig()->hasCustomPaymentSchedule) {
            if ($outstandingLateInterestAmount > $this->outstandingLateInterestAmount) {
                return $outstandingLateInterestAmount;
//                return array_sum([
//                    $outstandingLateInterestAmount,
//                    $this->outstandingLateInterestAmount
//                ]);
            }
            return $this->outstandingLateInterestAmount;
        }

        $this->outstandingLateInterestAmount = $outstandingLateInterestAmount;

        return $this->outstandingLateInterestAmount;
    }

    public function isPaid(): bool
    {
        $isPaid = true;
        /** @phpstan-ignore-next-line */
        foreach ($this as $attr => $val) {
            if (in_array($attr, ['index', 'accruedLateAmounts'])) {
                continue;
            }

            /// check amounts for fees
            if ($this->$attr instanceof Collection) {
                foreach ($this->$attr as $loanFee) {
                    if ($loanFee->outstandingAmount !== 0) {
                        $isPaid = false;
                        break;
                    }
                }
            }

            if (is_numeric($this->$attr) && $this->$attr !== 0) {
                $isPaid = false;
                break;
            }
        }

        return $isPaid;
    }
}
