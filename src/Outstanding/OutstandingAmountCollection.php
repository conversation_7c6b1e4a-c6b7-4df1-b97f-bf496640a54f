<?php

namespace StikCredit\Calculators\Outstanding;

use StikCredit\Calculators\Abstract\AbstractLoanCollection;
use StikCredit\Calculators\Accrued\AccruedAmountsCollection;
use StikCredit\Calculators\Accrued\DefaultAccruedAmount;
use StikCredit\Calculators\Fees\DefaultFee;
use StikCredit\Calculators\Fees\FeesCollection;
use StikCredit\Calculators\Installments\DefaultInstallment;
use StikCredit\Calculators\Installments\InstallmentsCollection;

/**
 * @method DefaultOutstanding first()
 * @method DefaultOutstanding last()
 * @method DefaultOutstanding get(int $key, $default = null)
 */
class OutstandingAmountCollection extends AbstractLoanCollection
{
    public function build(array $hasCustomPaymentSchedule): OutstandingAmountCollection
    {
        if (!empty($hasCustomPaymentSchedule)) {
            return $this->buildFromCustomSchedule($hasCustomPaymentSchedule);
        }

        /** @var DefaultAccruedAmount[]|AccruedAmountsCollection $accruedAmountsCollection * */
        $accruedAmountsCollection = $this->diContainer->get(AccruedAmountsCollection::class);
        $installmentsCollection = $this->diContainer->get(InstallmentsCollection::class);

        /** @var DefaultFee[]|FeesCollection $feesCollection * */
        $feesCollection = $this->diContainer->get(FeesCollection::class);


        $installmentsCollection->each(function (DefaultInstallment $defaultInstallment, int $index)
        use ($feesCollection, $accruedAmountsCollection) {

            $defaultAccruedAmount = $accruedAmountsCollection->get($index);

            $outstandingAmount = new DefaultOutstanding(
                $defaultInstallment->index,
                $defaultInstallment->principal,
                $defaultInstallment->interest,
                $defaultInstallment->penaltyAmount,
                /// late interest
                $defaultAccruedAmount->accruedLateInterestAmount,
                $defaultAccruedAmount->accruedLatePenaltyAmount,
            );

            /// grouped outstanding fees amount
            $outstandingAmount->outstandingGroupedFees = $feesCollection->getDueFeesToInstallment($index);
            $outstandingAmount->accruedLateAmounts = collect([]);

            /// put object to collection
            $this->put($index, $outstandingAmount);
        });

        return $this;
    }

    public function buildFromCustomSchedule(array $hasCustomPaymentSchedule): OutstandingAmountCollection
    {
        /** @var FeesCollection $feesCollection * */
        $feesCollection = $this->diContainer->get(FeesCollection::class);

        $this->diContainer
            ->get(AccruedAmountsCollection::class)
            ->each(function (DefaultAccruedAmount $defaultAccruedAmount, int $index) use ($hasCustomPaymentSchedule, $feesCollection) {

                if ($defaultAccruedAmount->isPaid()) {
                    return true;
                }

                $accruedPrincipleAmount = $defaultAccruedAmount->accruedPrincipleAmount;
                $accruedInterestAmount = $hasCustomPaymentSchedule[$index]['accrued_interest'];
                $accruedPenaltyAmount = $hasCustomPaymentSchedule[$index]['accrued_penalty'];
                $accruedLateInterestAmount = $hasCustomPaymentSchedule[$index]['late_interest'];
                $accruedLatePenaltyAmount = $hasCustomPaymentSchedule[$index]['late_penalty'];

                if ($defaultAccruedAmount->isPartiallyPaid()) {
                    $defaultOutstandingAmount = $this->get($defaultAccruedAmount->index);


                    $accruedPrincipleAmount = $defaultOutstandingAmount->outstandingPrincipleAmount;
                    $accruedInterestAmount = $defaultOutstandingAmount->outstandingInterestAmount;
                    $accruedPenaltyAmount = $defaultOutstandingAmount->outstandingPenaltyAmount;
                    /// late interest
                    $accruedLateInterestAmount = $defaultOutstandingAmount->getOutstandingLateInterestAmount();
                    $accruedLatePenaltyAmount = $defaultOutstandingAmount->getOutstandingLatePenaltyAmount();
                }

                $outstandingAmount = new DefaultOutstanding(
                    $defaultAccruedAmount->index,
                    $accruedPrincipleAmount,
                    $accruedInterestAmount,
                    $accruedPenaltyAmount,
                    /// late interest
                    $accruedLateInterestAmount,
                    $accruedLatePenaltyAmount,
                );

                /// grouped outstanding fees amount
                $outstandingAmount->outstandingGroupedFees = $feesCollection->getDueFeesToInstallment($index);
                $outstandingAmount->accruedLateAmounts->push([
                    'outstandingLateInterestAmount' => $accruedLateInterestAmount,
                    'outstandingLatePenaltyAmount' => $accruedLatePenaltyAmount,
                ]);

                /// put object to collection
                $this->put($index, $outstandingAmount);

            });

        return $this;
    }

    public function buildFromAccrued(): OutstandingAmountCollection
    {
        /** @var AccruedAmountsCollection $accruedAmountsCollection * */
        $accruedAmountsCollection = $this->diContainer->get(AccruedAmountsCollection::class);

        /** @var FeesCollection $feesCollection * */
        $feesCollection = $this->diContainer->get(FeesCollection::class);


        $accruedAmountsCollection->each(function (DefaultAccruedAmount $defaultAccruedAmount, int $index)
        use ($feesCollection) {
            if ($defaultAccruedAmount->isPaid()) {
                return true;
            }

            $accruedPrincipleAmount = $defaultAccruedAmount->accruedPrincipleAmount;
            $accruedInterestAmount = $defaultAccruedAmount->accruedInterestAmount;
            $accruedPenaltyAmount = $defaultAccruedAmount->accruedPenaltyAmount;
            $accruedLateInterestAmount = $defaultAccruedAmount->accruedLateInterestAmount;
            $accruedLatePenaltyAmount = $defaultAccruedAmount->accruedLatePenaltyAmount;

            if ($defaultAccruedAmount->isPartiallyPaid()) {
                $defaultOutstandingAmount = $this->get($defaultAccruedAmount->index);


                $accruedPrincipleAmount = $defaultOutstandingAmount->outstandingPrincipleAmount;
                $accruedInterestAmount = $defaultOutstandingAmount->outstandingInterestAmount;
                $accruedPenaltyAmount = $defaultOutstandingAmount->outstandingPenaltyAmount;
                /// late interest
                $accruedLateInterestAmount = $defaultOutstandingAmount->getOutstandingLateInterestAmount();
                $accruedLatePenaltyAmount = $defaultOutstandingAmount->getOutstandingLatePenaltyAmount();
            }

            $outstandingAmount = new DefaultOutstanding(
                $defaultAccruedAmount->index,
                $accruedPrincipleAmount,
                $accruedInterestAmount,
                $accruedPenaltyAmount,
                /// late interest
                $accruedLateInterestAmount,
                $accruedLatePenaltyAmount,
            );

            /// grouped outstanding fees amount
            $outstandingAmount->outstandingGroupedFees = $feesCollection->getDueFeesToInstallment($index);
            $outstandingAmount->accruedLateAmounts = collect([]);

            /// put object to collection
            $this->put($index, $outstandingAmount);
        });

        return $this;
    }

    public function setLateAmountsFromCustom(array $hasCustomPaymentSchedule): void
    {
        $this->each(function (DefaultOutstanding $defaultOutstanding) use ($hasCustomPaymentSchedule) {
            $defaultOutstanding->outstandingLateInterestAmount = 0;
            $defaultOutstanding->outstandingLatePenaltyAmount = 0;

            collect($hasCustomPaymentSchedule)->each(function ($row) use ($defaultOutstanding) {
                if ($row['seq_num'] === $defaultOutstanding->index) {
                    $defaultOutstanding->outstandingLateInterestAmount = $row['late_interest'];
                    $defaultOutstanding->outstandingLatePenaltyAmount = $row['late_penalty'];

                    $defaultOutstanding->accruedLateAmounts->push([
                        'outstandingLateInterestAmount' => $defaultOutstanding->outstandingLateInterestAmount,
                        'outstandingLatePenaltyAmount' => $defaultOutstanding->outstandingLatePenaltyAmount,
                    ]);
                }
            });
        });
    }

    public function getOutstandingByKey(string $key): int
    {
        return $this->sum(function (DefaultOutstanding $defaultOutstanding) use ($key) {
            if (!property_exists($defaultOutstanding, $key)) {
                throw new \Exception('Invalid property DefaultOutstanding' . $key);
            }

            $defaultInstallment = $this->getInstallment($defaultOutstanding->index);
            if ($defaultInstallment->isDue() && !$defaultOutstanding->isPaid()) {
                return $defaultOutstanding->$key;
            }
        });
    }

    public function getTotalOutstandingLateAmount(): int
    {
        return array_sum([
            $this->getOutstandingLatePenalty(),
            $this->getOutstandingLateInterest()
        ]);
    }

    public function getOutstandingLatePenalty(): int
    {
        return $this->sum(function (DefaultOutstanding $defaultOutstanding) {
            return $defaultOutstanding->getOutstandingLatePenaltyAmount();
        });
    }

    public function getOutstandingLateInterest(): int
    {
        return $this->sum(function (DefaultOutstanding $defaultOutstanding) {
            return $defaultOutstanding->getOutstandingLateInterestAmount();
        });
    }

    public function getTotalOutstandingFeesAmount(): int
    {
        return $this->sum(function (DefaultOutstanding $defaultOutstanding) {
            return $defaultOutstanding->outstandingGroupedFees->sum('outstandingAmount');
        });
    }

    public function getTotalOutstandingAmount(): int
    {
        return $this->sum(function (DefaultOutstanding $defaultOutstanding) {
            return $defaultOutstanding->getTotalOutstandingAmount();
        });
    }
}
