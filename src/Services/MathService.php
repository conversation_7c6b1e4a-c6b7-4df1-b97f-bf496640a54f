<?php

namespace StikCredit\Calculators\Services;

use StikCredit\Calculators\Config\LoanConfig;

class MathService
{
    protected LoanConfig $loanConfig;

    public function __construct()
    {
        $this->loanConfig = LoanConfig::instance();
    }

    public function calcDailyAmount(int $passedDays, $installmentDays, int $amount): int
    {
        return (int)round(($passedDays / $installmentDays * $amount));
    }

    public function calcAccruedLateAmount(int $amount, int $overdueDays): int
    {
        $loanConfig = $this->loanConfig;

        $lateInterest = ($loanConfig->lateInterest / 360);
        $accruedLateAmount = ($overdueDays * $lateInterest * $amount);
        return (int)round($accruedLateAmount, 4);
    }

    public function calculateAmount(int $amount, float $interestRate): int
    {
        return (int)round(($amount * $interestRate));
    }

    public function getInstallmentAmount(): int
    {
        $loanConfig = $this->loanConfig;

        $interestRate = $this->getInterestRate();
        $period = $loanConfig->numberOfInstallments;

        $interest = $interestRate * pow(1 + $interestRate, $period) / (pow(1 + $interestRate, $period) - 1);

        return (int)round($interest * $loanConfig->amount);
    }

    public function getPenaltyInterestRate(): float
    {
        $loanConfig = $this->loanConfig;
        $divider = $loanConfig->divider;
        $discountPercent = $loanConfig->discountPercent;
        $numberOfInstallments = $loanConfig->numberOfInstallments;
        if ($loanConfig->isProductType('payday')) {
            return round($loanConfig->apr / $divider * (1 - $discountPercent) * $numberOfInstallments, 4);
        }

        if ($loanConfig->hasDiscount()) {
            return round(($loanConfig->apr * (1 - $discountPercent) / $divider), 4);
        }
        return round(($loanConfig->apr / $divider), 4);
    }

    public function getAirInterestRate(): float
    {
        $loanConfig = $this->loanConfig;
        $divider = $loanConfig->divider;
        $discountPercent = $loanConfig->discountPercent;
        $numberOfInstallments = $loanConfig->numberOfInstallments;
        if ($loanConfig->isProductType('payday')) {
            return round($loanConfig->air / $divider * (1 - $discountPercent) * $numberOfInstallments, 4);
        }

        if ($loanConfig->hasDiscount()) {
            return round(($loanConfig->air * (1 - $discountPercent) / $divider), 4);
        }
        return round(($loanConfig->air / $divider), 4);
    }

    public function getInterestRate(): float
    {
        $loanConfig = $this->loanConfig;
        $divider = $loanConfig->divider;
        $discountPercent = $loanConfig->discountPercent;
        $numberOfInstallments = $loanConfig->numberOfInstallments;
        if ($loanConfig->isProductType('payday')) {
            return round(($loanConfig->air + $loanConfig->apr) / $divider * (1 - $discountPercent) * $numberOfInstallments, 4);
        }

        if ($loanConfig->hasDiscount()) {
            return round(((($loanConfig->air * (1 - $discountPercent)) + ($loanConfig->apr * (1 - $discountPercent))) / $divider), 4);
        }
        return round((($loanConfig->air + $loanConfig->apr) / $divider), 4);
    }


    public function calculateGpr(): float
    {
        $loanConfig = $this->loanConfig;
        $divider = $loanConfig->divider;
        $air = $loanConfig->air;
        if ($loanConfig->hasDiscount()) {
            $air = ($loanConfig->air * (1 - $loanConfig->discountPercent));
        }
        $gpr = (pow((1 + $air / $divider), $divider) - 1) * 100;

        return round($gpr, 2);
    }

}
