<?php

namespace StikCredit\Calculators\Services;

use StikCredit\Calculators\Config\LoanConfig;
use Illuminate\Support\Carbon;

class DateBuilder extends Carbon
{
    public function sqlDate(string|null $date = null, $format = null): Carbon|string
    {
        if ($format) {
            return (new Carbon($date))->format($format);
        }

        if (is_null($date)) {
            $date = LoanConfig::instance()->currentDate;
        }

        return new Carbon($date);
    }

    public function dateDiff(string $fromDate, string $toDate): int
    {
        $toDate = $this->sqlDate($toDate);

        return (int)$this->sqlDate($fromDate)->diff($toDate)->format('%a');
    }
}
