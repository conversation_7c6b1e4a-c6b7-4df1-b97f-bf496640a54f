# GitLab CI/CD Pipeline for Credit Hunter Laravel Application
# This pipeline handles testing, building, and running tests inside docker-compose

stages:
  - build
  - test

variables:
  SELF: .env
  VOLUME_PATH_PG: pgdata
  VOLUME_PATH_REDIS: redisdata
  DB_EXTERNAL_PORT: 5437
  NGINX_PORT: 80
  NGINX_PORT_SSL: 443
  REDIS_EXTERNAL_PORT: 6377

  # Docker in Docker configuration
  DOCKER_HOST: "tcp://docker:2375"
  DOCKER_TLS_CERTDIR: ""

  # Application variables
  APP_NAME: credit-hunter
  APP_USER: laravel
  APP_USER_ID: 1000
  TZ: Europe/Sofia

  # Database configuration for testing
  DB_CONNECTION: pgsql
  DB_HOST: postgres
  DB_PORT: 5432
  DB_DATABASE: credit_hunter_db
  DB_USERNAME: root
  DB_PASSWORD: w1ld)H@nt8
  POSTGRES_TEST_DB: credit_hunter_test
  POSTGRES_DEFAULT_USER: postgres
  POSTGRES_DEFAULT_PASS: postgres!DB_2020

  # Cache and session
  CACHE_DRIVER: redis
  SESSION_DRIVER: redis
  QUEUE_CONNECTION: redis
  REDIS_HOST: redis
  REDIS_PORT: 6379

  # Elasticsearch
  ELASTICSEARCH_HOST: elasticsearch
  ELASTICSEARCH_PORT: 9200
  ELASTIC_BOOT_PASSWORD: elastic

cache:
  key: ${CI_COMMIT_REF_SLUG}
  paths:
    - src/vendor/
    - src/node_modules/

# NOTE: Runner must be docker executor and privileged = true.
# Service alias "docker" is required so DOCKER_HOST tcp://docker:2375 resolves.

# -----------------------------------------------------------------------------
# Run tests using docker-compose (uses Project Access Token via .netrc, no SSH)
# -----------------------------------------------------------------------------
test_php:
  stage: test
  image: docker:20.10.24
  services:
    - name: docker:20.10.24-dind
      alias: docker
  before_script:
    - apk add --no-cache docker-compose curl jq
    - |
      if [ -n "$CI_REGISTRY" ] && [ -n "$CI_REGISTRY_USER" ]; then
        echo "$CI_REGISTRY_PASSWORD" | docker login -u "$CI_REGISTRY_USER" --password-stdin "$CI_REGISTRY"
      fi
    - |
      for i in $(seq 1 30); do
        if curl -sS --fail http://docker:2375/_ping >/dev/null 2>&1; then
          echo "dind ready"
          break
        fi
        echo "waiting for dind ($i)..."
        sleep 1
      done
    - docker info || true

    # make sure any previous volumes/containers from earlier runs are removed (fresh CI run)
    - docker-compose down -v --remove-orphans || true
    # Create .env file on host before starting containers (prevents Docker from creating it as directory)
    - cp src/.env.testing.example src/.env

    # build (ensures local images exist) then start services
    - docker-compose build --parallel
    - docker-compose up -d --remove-orphans

    # Wait for services to be ready
    - echo "Waiting for services to be ready..."
    - |
      for i in $(seq 1 60); do
        if docker-compose exec -T postgres pg_isready -U postgres >/dev/null 2>&1; then
          echo "PostgreSQL is ready"
          break
        fi
        echo "Waiting for PostgreSQL ($i/60)..."
        sleep 2
      done
    - |
      for i in $(seq 1 30); do
        if docker-compose exec -T redis redis-cli ping >/dev/null 2>&1; then
          echo "Redis is ready"
          break
        fi
        echo "Waiting for Redis ($i/30)..."
        sleep 1
      done
  script:
    # Clear any cached Laravel config
    - docker-compose exec -T app sh -lc 'rm -f bootstrap/cache/config.php bootstrap/cache/services.php || true'

    # Verify .env file is properly mounted and has correct content
    - docker-compose exec -T app sh -lc 'ls -la .env'
    - docker-compose exec -T app sh -lc 'head -5 .env'

    # Wait a bit more for database to be fully ready
    - sleep 10

    # Test database connection first
    - |
      for i in $(seq 1 30); do
        if docker-compose exec -T app sh -lc 'php -r "try { new PDO(\"pgsql:host=postgres;port=5432;dbname=credit_hunter_db\", \"root\", \"w1ld)H@nt8\"); echo \"OK\"; exit(0); } catch(Exception \$e) { exit(1); }"' >/dev/null 2>&1; then
          echo "Database connection successful"
          break
        fi
        echo "Waiting for database connection ($i/30)..."
        sleep 2
      done

    # Verify .env file has correct DB_HOST
    - docker-compose exec -T app sh -lc 'echo "Checking .env file:"; grep "DB_HOST" .env'

    # Install composer dependencies with explicit environment variables to override any defaults
    - docker-compose exec -T app sh -lc 'export DB_HOST=postgres DB_CONNECTION=pgsql DB_DATABASE=credit_hunter_db DB_USERNAME=root DB_PASSWORD="w1ld)H@nt8" && COMPOSER_DISABLE_XDEBUG_WARN=1 composer install --prefer-dist --no-interaction --no-progress'

    # Generate application key
    - docker-compose exec -T app sh -lc 'php artisan key:generate'

    # Clear any cached config
    - docker-compose exec -T app sh -lc 'php artisan config:clear'
    - docker-compose exec -T app sh -lc 'php artisan cache:clear'

    # Initialize the project
    - docker-compose exec -T app sh -lc 'php artisan project:init'

    # Run tests
    - docker-compose exec -T app sh -lc 'vendor/bin/phpunit --coverage-text --coverage-clover=coverage.xml'
  coverage: '/^\s*Lines:\s*(\d+\.\d+)%/'
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: src/coverage.xml
    paths:
      - src/coverage.xml
    expire_in: 1 week
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - if: $CI_COMMIT_BRANCH
