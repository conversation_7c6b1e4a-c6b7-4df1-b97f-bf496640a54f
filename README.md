### Example create installments loan

```php
        $loanConfig['air'] = 36;
        $loanConfig['apr'] = 126;
        $loanConfig['discountPercent'] = 18;
        $loanConfig['currentDate'] = date('Y-m-d');
        $loanConfig['utilisationDate'] = '2022-07-01';
        $loanConfig['startFromDate'] = '2022-07-10';
        $loanConfig['installmentModifier'] = '+1 month'; /// ('+1 month', '+14 days', '+7 days')
        $loanConfig['amount'] = 200000; /// converted amount 2000.00
        $loanConfig['numberOfInstallments'] = 12;
        $loanConfig['productType'] = 'installment';

        /// Create loan first way
        $calc = new LoanCalculator($loanConfig);
        $calc->build(); /// return loan collections for more info see Calculator::class
        
        /// Create loan second way
        $calc = new LoanCalculator();
        $calc->build($loanConfig); /// return loan collections for more info see Calculator::class
```

### Example create payday loan

```php
        $loanConfig['air'] = 28.08;
        $loanConfig['apr'] = 252.72;
        $loanConfig['discountPercent'] = 0;
        $loanConfig['installmentModifier'] = '+14 days';
        $loanConfig['utilisationDate'] = '2021-02-01';
        $loanConfig['startFromDate'] = '2021-02-15';
        $loanConfig['currentDate'] = '2021-02-12';
        $loanConfig['amount'] = 20000;
        $loanConfig['numberOfInstallments'] = 1;
        $loanConfig['productType'] = 'payday';

        $sut = new LoanCalculator($loanConfig);
        $sut->build();
```

### Get all amounts

```php
        $loanConfig['air'] = (36 / 100);
        $loanConfig['apr'] = (126 / 100);
        $loanConfig['discountPercent'] = (18 / 100);
        $loanConfig['currentDate'] = '2022-10-21';
        $loanConfig['utilisationDate'] = '2022-07-01';
        $loanConfig['startFromDate'] = '2022-07-10';
        $loanConfig['installmentModifier'] = '+1 month';
        $loanConfig['amount'] = 200000;
        $loanConfig['numberOfInstallments'] = 12;
        $loanConfig['productType'] = 'installment';
        $loanConfig['receivedPayments'] = [
            [
                'paymentId' => 51,
                'createdAt' => '2022-07-10',
                'amount' => 55000,
            ],
            [
                'paymentId' => 991,
                'createdAt' => '2022-09-15',
                'amount' => 110000,
            ],
            [
                'paymentId' => 992,
                'createdAt' => '2022-11-25',
                'amount' => 2090,
            ],
            [
                'paymentId' => 993,
                'createdAt' => '2023-01-15',
                'amount' => 130000,
            ],
        ];

        $sut = new LoanCalculator($loanConfig);
        $sut->build(); /// build calculator
        
        /// carton will be return object with grouped amounts
        /**
            public float $gpr = 0.0,
            public int   $principle = 0,
            public int   $duePrinciple = 0,
            public int   $paidPrinciple = 0,
            public int   $outstandingPrinciple = 0,
            /// interest
            public int   $interest = 0,
            public int   $dueInterest = 0,
            public int   $paidInterest = 0,
            public int   $outstandingInterest = 0,
            /// penalty
            public int   $penalty = 0,
            public int   $duePenalty = 0,
            public int   $paidPenalty = 0,
            public int   $outstandingPenalty = 0,
            /// late interest
            public int   $latePenalty = 0,
            public int   $lateInterest = 0,
            /// early repayment
            public int   $earlyRepaymentAmount = 0,
            /// overdue days
            public int   $maxOverdueDays = 0,
            public int   $totalOverdueDays = 0,
         */
        $loanCarton = $sut->getLoanCarton();
```
