# Private Package (stik-credit/calculators) Fix

## The Problem

The error was:
```
Target class [StikCredit\Calculators\LoanCalculator] does not exist.
```

This happened during `php artisan project:init` when <PERSON><PERSON> tried to resolve the `LoanCalculator` class.

## Root Cause Analysis

Looking at your `composer.json`, the `stik-credit/calculators` package is configured as:

1. **Path Repository** (preferred):
   ```json
   "repositories": [
       {
           "type": "path",
           "url": "./custom-repos/calculators"
       }
   ]
   ```

2. **Git Repository** (fallback in composer.lock):
   ```
   "url": "**********************:stikcredit-dev-team/stik-calcs.git"
   ```

The issue was that:
- The **local path repository exists** (`src/custom-repos/calculators/`)
- The **LoanCalculator.php file exists** in the local package
- But the **autoloader wasn't finding it** properly after composer install

## Why This Happened

1. **Autoloader Not Regenerated**: After composer install, the autoloader might not include the local path repository properly
2. **Path Repository Priority**: Composer might be confused between the path repository and Git repository
3. **Class Loading**: Laravel's container couldn't resolve the class because it wasn't in the autoloader

## The Fix Applied

### 1. **Verify Local Package Exists**
```yaml
# Check that the local calculators package exists
- docker-compose exec -T app sh -lc 'echo "Checking local calculators package:"; ls -la custom-repos/calculators/src/LoanCalculator.php'
```

### 2. **Regenerate Autoloader**
```yaml
# Regenerate autoloader to ensure local packages are properly loaded
- docker-compose exec -T app sh -lc 'composer dump-autoload --optimize'
```

### 3. **Verify Class Loading**
```yaml
# Verify the LoanCalculator class is available
- docker-compose exec -T app sh -lc 'php -r "echo \"Checking LoanCalculator class...\n\"; if (class_exists(\"StikCredit\\\\Calculators\\\\LoanCalculator\")) { echo \"✅ LoanCalculator class found\n\"; } else { echo \"❌ LoanCalculator class NOT found\n\"; exit(1); }"'
```

## How Path Repositories Work

Path repositories in Composer allow you to:
- **Use local packages** instead of downloading from remote repositories
- **Develop packages locally** while using them in your main project
- **Override remote packages** with local versions

Your configuration:
```json
{
    "repositories": [
        {
            "type": "path",
            "url": "./custom-repos/calculators"
        }
    ],
    "require": {
        "stik-credit/calculators": "dev-master"
    }
}
```

This tells Composer:
1. Look for `stik-credit/calculators` in `./custom-repos/calculators` first
2. If not found, fall back to other repositories (like Git)

## Expected Results

Now your pipeline should show:
```
✅ Checking local calculators package:
✅ -rw-r--r-- 1 <USER> <GROUP> 1234 custom-repos/calculators/src/LoanCalculator.php
✅ Composer install successful
✅ Autoloader regenerated
✅ ✅ LoanCalculator class found
✅ php artisan project:init successful
✅ Tests run successfully
```

## Why This Approach Works

1. **Local Package Available**: The calculators package exists locally, no need for Git access
2. **Autoloader Regeneration**: Ensures all local packages are properly included
3. **Class Verification**: Confirms the class is loadable before proceeding
4. **No External Dependencies**: Doesn't require access to private Git repositories

## Alternative Solutions (if this doesn't work)

If the path repository still doesn't work, you could:

1. **Copy the package directly**:
   ```bash
   cp -r custom-repos/calculators/src/* vendor/stik-credit/calculators/src/
   ```

2. **Use autoload-dev for local development**:
   ```json
   "autoload-dev": {
       "psr-4": {
           "StikCredit\\Calculators\\": "custom-repos/calculators/src/"
       }
   }
   ```

Your pipeline should now work without the "Target class does not exist" error! 🚀
